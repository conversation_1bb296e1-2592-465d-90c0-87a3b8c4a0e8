import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'node:path'

export default defineConfig({
    base: './',
    plugins: [
        vue(),
        createSvgIconsPlugin({
            iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
            symbolId: 'icon-[dir]-[name]',
            inject: 'body-first',
            customDomId: '__svg__icons__dom__',
        }),
    ],
    server: {
        port: 3003,
        host: '0.0.0.0',
        open: true,
        hmr: {
            overlay: false
        }
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src'),
        },
    },
    build: {
        chunkSizeWarningLimit: 4000,
        rollupOptions: {
            output: {
                manualChunks: {
                    'vendor-vue': ['vue', 'vue-router', 'vuex'],
                    'vendor-ui': ['element-plus'],
                    'vendor-charts': ['echarts'],
                    'vendor-others': ['jsencrypt', 'axios'],

                    components: [
                        './src/components/layout/HeaderComponent.vue',
                        './src/components/statistics/DeviceStatusList.vue',
                        './src/components/statistics/DataTable.vue'
                    ],
                    utils: [
                        './src/utils/request.js',
                        './src/utils/ueInterface.js',
                        './src/utils/svgIcon.js',
                        './src/utils/permission.js'
                    ]
                },
                entryFileNames: 'assets/[name].[hash].js',
                chunkFileNames: 'assets/[name].[hash].js',
                assetFileNames: 'assets/[name].[hash].[ext]'
            }
        },
        minify: 'esbuild',
        esbuildOptions: {
            drop: ['debugger'],
            pure: [],
            legalComments: 'none'
        }
    }
}) 