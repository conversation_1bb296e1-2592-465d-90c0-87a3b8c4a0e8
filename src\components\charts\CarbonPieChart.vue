<template>
    <div ref="chartRef" class="carbon-pie-chart"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    totalValue: {
        type: [Number, String],
        default: 0
    }
});

const chartRef = ref(null);
let chart = null;

const initChart = () => {
    if (!chartRef.value) return;
    
    chart = echarts.init(chartRef.value);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {d}%'
        },
        series: [
            {
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['50%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 2,
                    borderWidth: 2,
                    borderColor: 'rgba(0, 0, 0, 0.2)'
                },
                label: {
                    show: false
                },
                data: [
                    { value: 39.68, name: '空调', itemStyle: { color: '#FF6B6B' } },
                    { value: 37.88, name: '照明插座', itemStyle: { color: '#4ECDC4' } },
                    { value: 15.88, name: '动力', itemStyle: { color: '#FFD93D' } },
                    { value: 5.85, name: '特殊', itemStyle: { color: '#95A5A6' } },
                    { value: 0.71, name: '用水', itemStyle: { color: '#6C5CE7' } }
                ]
            }
        ]
    };

    chart.setOption(option);
};

onMounted(() => {
    initChart();
    window.addEventListener('resize', () => chart?.resize());
});

onUnmounted(() => {
    chart?.dispose();
    window.removeEventListener('resize', () => chart?.resize());
});
</script>

<style scoped>
.carbon-pie-chart {
    width: 100%;
    height: 300px;
}
</style> 