import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import store from './store'
import * as echarts from 'echarts'
import { setupSvgIcon } from './utils/svgIcon'
import 'virtual:svg-icons-register'
import './assets/styles/fonts.less'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

// 注册 SVG 图标组件
setupSvgIcon(app)

// 全局挂载 echarts
app.config.globalProperties.$echarts = echarts

app.use(ElementPlus)
app.use(router)
app.use(store)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue 错误:', err)
  console.error('错误信息:', info)
}

window.addEventListener('unhandledrejection', event => {
  console.error('未处理的 Promise 拒绝:', event.reason)
})

// 等待路由就绪后再挂载应用
router.isReady().then(() => {
    app.mount('#app')
}) 