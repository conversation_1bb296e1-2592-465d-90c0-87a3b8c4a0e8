import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

/**
 * RSA加密
 * @param {string} publicKey - 公钥
 * @param {string} data - 需要加密的数据
 * @returns {string} 加密后的数据
 */
export function encryptData(publicKey, data) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  return encryptor.encrypt(data)
}

/**
 * 获取公钥
 * @returns {Promise<string>} 公钥
 */
export async function getPublicKey() {
  try {
    const response = await fetch('http://47.101.156.19:9101/CZC/?_req=PUB_REQ')
    const data = await response.json()
    if (data.code === 200) {
      const publicKey = data.data
      sessionStorage.setItem('publicKey', publicKey)
      return publicKey
    }
    throw new Error(data.message || '获取公钥失败')
  } catch (error) {
    console.error('获取公钥错误:', error)
    throw error
  }
} 