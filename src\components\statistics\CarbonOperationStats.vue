<template>
    <div class="carbon-operation-stats">
        <BehaviorChart @openSceneDetail="$emit('openSceneDetail')" :timeDimension="timeDimension"
            :selectedBuilding="selectedBuilding" />
    </div>
</template>

<script setup>
import { defineEmits, defineProps } from 'vue'
import Behavior<PERSON>hart from '../charts/BehaviorChart.vue'

defineProps({
    timeDimension: {
        type: String,
        required: true,
        default: 'day'
    },
    selectedBuilding: {
        type: String,
        default: 'A1#'
    }
})

defineEmits(['openSceneDetail'])
</script>

<style lang="less" scoped>
.carbon-operation-stats {
    width: 100%;
    height: 100%;
}
</style>