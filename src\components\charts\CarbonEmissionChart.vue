<template>
    <div class="carbon-emission-chart">

        <div ref="chartRef" class="chart-container"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import zexian1 from '@/assets/images/zexian1.png'
import zexian2 from '@/assets/images/zexian2.png'

const chartRef = ref(null)
let chart = null

const props = defineProps({
    dimension: {
        type: String,
        default: 'month'
    }
});

// 先定义 loadChartData 函数
const loadChartData = (dimension) => {
    if (!chart) return;

    let xAxisData = [];
    let directEmissions = [];
    let indirectEmissions = [];

    // 根据维度设置不同的数据
    switch (dimension) {
        case 'month':
            xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            directEmissions = [1800, 3000, 2600, 1800, 1400, 1300, 2000, 2400, 2800, 3200, 2900, 2700];
            indirectEmissions = [2600, 3400, 1800, 2600, 2800, 3200, 2900, 2700, 2500, 2300, 2100, 2000];
            break;
        case 'quarter':
            xAxisData = ['第一季度', '第二季度', '第三季度', '第四季度'];
            directEmissions = [2500, 1800, 2200, 2900];
            indirectEmissions = [2800, 2600, 2700, 2200];
            break;
        case 'year':
            xAxisData = ['2025', '2026', '2027', '2028', '2029'];
            directEmissions = [2800, 2600, 2400, 2200, 2000];
            indirectEmissions = [3000, 2800, 2600, 2400, 2200];
            break;
    }

    // 更新图表配置
    const option = {
        ...chart.getOption(),
        xAxis: {
            ...chart.getOption().xAxis[0],
            data: xAxisData
        },
        series: [
            {
                ...chart.getOption().series[0],
                data: directEmissions
            },
            {
                ...chart.getOption().series[1],
                data: indirectEmissions
            }
        ]
    };

    chart.setOption(option);
};

// 然后再设置 watch
watch(() => props.dimension, (newDimension) => {
    loadChartData(newDimension);
}, { immediate: true });

const initChart = () => {
    if (!chartRef.value) return

    chart = echarts.init(chartRef.value)
    const option = {
        grid: {
            top: 40,
            left: 10,
            right: 10,
            bottom: 20,
            containLabel: true
        },
        legend: {
            data: ['直接排放', '间接排放'],
            textStyle: { color: '#fff' },
            top: 0,
            left: 0,
            icon: 'rect',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 25,
            textGap: 10,
        },
        xAxis: {
            type: 'category',
            data: ['2月', '4月', '6月', '8月', '10月', '12月'],
            axisLine: {
                lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
            },
            axisLabel: { color: '#fff' }
        },
        yAxis: {
            type: 'value',
            max: 4000,
            interval: 1000,
            axisLine: {
                show: false
            },
            axisLabel: { color: '#fff' },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        series: [
            {
                name: '直接排放',
                type: 'line',
                smooth: false,
                symbol: 'image://' + zexian1,
                symbolSize: [20, 20],
                data: [1800, 3000, 2600, 1800, 1400, 1300],
                lineStyle: {
                    width: 2,
                    color: '#409EFF'
                },
                itemStyle: {
                    color: '#409EFF',
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                        { offset: 1, color: 'rgba(64, 158, 255, 0)' }
                    ])
                }
            },
            {
                name: '间接排放',
                type: 'line',
                smooth: false,
                symbol: 'image://' + zexian2,
                symbolSize: [20, 20],
                data: [2600, 3400, 1800, 2600, 2800, 3200],
                lineStyle: {
                    width: 2,
                    color: '#67C23A'
                },
                itemStyle: {
                    color: '#67C23A',
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
                        { offset: 1, color: 'rgba(103, 194, 58, 0)' }
                    ])
                }
            }
        ]
    }

    chart.setOption(option)

    // 初始化后立即加载对应维度的数据
    loadChartData(props.dimension);
}

onMounted(() => {
    initChart()
    window.addEventListener('resize', initChart)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }
    window.removeEventListener('resize', initChart)
})
</script>

<style lang="less" scoped>
.carbon-emission-chart {
    height: 250px;

    // backdrop-filter: blur(14px);

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;



        .unit {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }
    }


}

.chart-container {
    width: 100%;
    height: 100%;
}
</style>