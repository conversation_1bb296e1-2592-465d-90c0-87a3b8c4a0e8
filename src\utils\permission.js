/**
 * 根据角色过滤路由
 * @param {Array} routes - 异步路由表
 * @param {Array} roles - 用户角色
 */
export function filterAsyncRoutes(routes, roles) {
    const res = []

    routes.forEach(route => {
        const tmp = { ...route }
        if (hasPermission(roles, tmp)) {
            if (tmp.children) {
                tmp.children = filterAsyncRoutes(tmp.children, roles)
            }
            res.push(tmp)
        }
    })

    return res
}

/**
 * 判断是否有权限
 * @param {Array} roles - 用户角色
 * @param {Object} route - 路由对象
 */
function hasPermission(roles, route) {
    if (route.meta && route.meta.roles) {
        return roles.some(role => route.meta.roles.includes(role))
    }
    return true
} 