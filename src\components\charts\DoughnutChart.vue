<template>
    <div class="chart-container panel">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null

const initChart = () => {
    if (chartRef.value) {
        chart = echarts.init(chartRef.value)
        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{b}: {c}%',
                backgroundColor: 'rgba(6, 30, 93, 0.9)',
                borderColor: '#4B96FF',
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                orient: 'horizontal',
                bottom: '5%',
                left: 'center',
                itemWidth: 12,
                itemHeight: 12,
                textStyle: {
                    color: '#fff',
                    fontSize: 12
                },
                data: ['空调', '照明插座', '动力', '特殊']
            },
            title: {
                text: '15,315',
                subtext: 'kgCO₂e',
                left: 'center',
                top: '40%',
                textStyle: {
                    color: '#fff',
                    fontSize: 34,
                    fontWeight: 'bold'
                },
                subtextStyle: {
                    color: '#fff',
                    fontSize: 20,
                    fontWeight: 'normal'
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: ['55%', '70%'],
                    center: ['50%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: true,
                        position: 'outside',
                        formatter: '{d}%',
                        color: '#fff',
                        fontSize: 14
                    },
                    labelLine: {
                        show: true,
                        length: 15,
                        length2: 10,
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    data: [
                        {
                            value: 39.97,
                            name: '空调',
                            itemStyle: { color: '#FF7875' }  // 红色
                        },
                        {
                            value: 38.15,
                            name: '照明插座',
                            itemStyle: { color: '#7B9FF2' }  // 蓝色
                        },
                        {
                            value: 15.99,
                            name: '动力',
                            itemStyle: { color: '#FFD666' }  // 黄色
                        },
                        {
                            value: 5.89,
                            name: '特殊',
                            itemStyle: { color: '#87E8DE' }  // 青色
                        }
                    ],
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 16,
                            fontWeight: 'bold'
                        }
                    }
                }
            ]
        }
        chart.setOption(option)
    }
}

const handleResize = () => {
    chart?.resize()
}

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    chart?.dispose()
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-container {
    .chart {
        height: 310px !important;
        width: 100%;
    }
}
</style>