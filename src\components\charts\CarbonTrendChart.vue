<template>
    <div class="chart-container">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let resizeObserver = null

const initChart = async () => {
    if (!chartRef.value) return

    await nextTick()

    if (chart) {
        chart.dispose()
    }

    chart = echarts.init(chartRef.value)
    const option = {
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 30, 93, 0.9)',
            borderColor: '#4B96FF',
            textStyle: {
                color: '#fff'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>'
                params.forEach(param => {
                    result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`
                    result += `${param.seriesName}: ${param.value}<br/>`
                })
                return result
            }
        },
        legend: {
            data: [
                {
                    name: '碳排放总量',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    }
                },
                {
                    name: '碳减排量',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    }
                },
                {
                    name: '实际碳排放量',
                    textStyle: {
                        color: '#4B96FF',
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                }
            ],
            left: '5%',
            top: '0%',
            itemWidth: 20,
            itemHeight: 8,
            itemGap: 15
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        yAxis: {
            type: 'value',
            min: -5000,
            max: 10000,
            interval: 2000,
            axisLine: {
                show: false
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12,
                formatter: '{value}'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        series: [
            {
                name: '碳排放总量',
                type: 'line',
                smooth: false,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#FFB84D'
                },
                lineStyle: {
                    width: 1,
                    color: '#FFB84D'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(255, 184, 77, 0.3)' },
                        { offset: 1, color: 'rgba(255, 184, 77, 0.1)' }
                    ])
                },
                data: [9000, 7000, 8500, 5500, 8000, 9500, 7000, 9000, 7500, 8000, 6500, 6000]
            },
            {
                name: '碳减排量',
                type: 'line',
                smooth: false,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#52C41A'
                },
                lineStyle: {
                    width: 1,
                    color: '#52C41A'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                        { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
                    ])
                },
                // 碳减排量为负数，表示减少的排放
                data: [-4000, -3000, -2500, -3500, -3800, -4000, -3000, -2000, -3200, -3500, -3800, -4000]
            },
            {
                name: '实际碳排放量',
                type: 'bar',
                barWidth: '30%',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#4B96FF' },
                        { offset: 1, color: '#1E5FFF' }
                    ]),
                    borderRadius: [4, 4, 0, 0]
                },
                emphasis: {
                    focus: 'series',
                    itemStyle: {
                        shadowBlur: 20,
                        shadowColor: '#4B96FF'
                    }
                },
                // 实际碳排放量 = 碳排放总量 + 碳减排量
                data: [5000, 4000, 6000, 2000, 4200, 5500, 4000, 7000, 4300, 4500, 2700, 2000]
            }
        ]
    }
    chart.setOption(option)

    // 添加动画效果让实际碳排放量更突出
    setTimeout(() => {
        chart.setOption({
            animation: true,
            animationDuration: 2000,
            animationEasing: 'elasticOut'
        })
    }, 100)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

onMounted(async () => {
    await initChart()

    resizeObserver = new ResizeObserver(() => {
        handleResize()
    })

    if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
    }

    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }

    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }

    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    
    .chart {
        height: 100%;
        width: 100%;
    }
}
</style> 