<template>
  <div class="carbon-management page-container">
    <title-component>碳管理系统</title-component>
    <div class="content flex-column">
      <statistics-cards />
      <div class="management-content panel">
        <el-tabs type="border-card" class="custom-tabs">
          <el-tab-pane label="设备管理">
            <device-status-list />
          </el-tab-pane>
          <el-tab-pane label="报警管理">
            <data-table />
          </el-tab-pane>
          <el-tab-pane label="统计分析">
            <div class="charts-container grid-container">
              <bar-chart />
              <line-chart />
              <doughnut-chart />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import TitleComponent from '../components/common/TitleComponent.vue'
import StatisticsCards from '../components/statistics/StatisticsCards.vue'
import DeviceStatusList from '../components/statistics/DeviceStatusList.vue'
import DataTable from '../components/statistics/DataTable.vue'
import BarChart from '../components/charts/BarChart.vue'
import LineChart from '../components/charts/LineChart.vue'
import DoughnutChart from '../components/charts/DoughnutChart.vue'
</script>

<style lang="less" scoped>
.carbon-management {
  .charts-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;

    .chart-item {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      padding: 15px;
    }
  }
}
</style> 