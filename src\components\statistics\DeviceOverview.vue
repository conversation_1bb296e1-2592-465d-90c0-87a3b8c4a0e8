<template>
  <div class="device-overview">
    <div class="device-grid">
      <div class="device-item" v-for="(item, index) in deviceItems" :key="index">
        <div class="icon-container">
          <svg-icon :icon-class="item.iconClass" :class="item.iconClassType" />
        </div>
        <div class="content-container">
          <div class="device-value">{{ item.value }}</div>
          <div class="device-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'DeviceOverview',
  data() {
    return {
      deviceItems: [
        {
          iconClass: 'shebei1',
          iconClassType: 'device-icon-1',
          value: '20',
          label: '环境监测设备(个)'
        },
        {
          iconClass: 'shebei2',
          iconClassType: 'device-icon-2',
          value: '100',
          label: '光伏装机容量(kW)'
        },
        {
          iconClass: 'shebei3',
          iconClassType: 'device-icon-3',
          value: '100',
          label: '能源监测设备(个)'
        },
        {
          iconClass: 'shebei4',
          iconClassType: 'device-icon-4',
          value: '8',
          label: '智能照明回路(条)'
        },
        {
          iconClass: 'shebei5',
          iconClassType: 'device-icon-5',
          value: '12',
          label: '空调室内机(台)'
        },
        {
          iconClass: 'shebei6',
          iconClassType: 'device-icon-6',
          value: '25',
          label: '充电桩装机(kW)'
        }
      ]
    }
  },

}
</script>

<style lang="less" scoped>
.device-overview {
  .device-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 12px;
    margin: 10px 0;
    .device-item {
      display: flex;
      align-items: center;
      gap: 12px;
      background-color: #d8d8d80d;
 
      padding: 11px 12px;

      .icon-container {
        position: relative;
        width: 39px;
        height: 41px;
        display: flex;
        align-items: center;
        justify-content: center;

        // 四角扫描框效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: 
            linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 100% 1px,
            linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 100% / 100% 1px,
            linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 1px 100%,
            linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 100% 0 / 1px 100%;
          background-repeat: no-repeat;
          animation: scanCorners 3s ease-in-out infinite;
          pointer-events: none;
        }

        .device-icon-1,
        .device-icon-2,
        .device-icon-3,
        .device-icon-4,
        .device-icon-5,
        .device-icon-6 {
          width: 23px;
          height: 19px;
          z-index: 1;
        }
      }

      .content-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .device-value {
          font-family: D-DIN;
          font-size: 24px;
          font-weight: bold;
          color: #FFE5A8;
          margin-bottom: 5px;
          text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
          transition: all 0.3s ease;
        }

        .device-label {
          font-family: SourceHanSans;
          font-size: 12px;
          font-weight: bold;
          color: #ffffff;
          opacity: 0.7;
          transition: all 0.3s ease;
        }
      }
    }
  }
}

// 扫描框动画
@keyframes scanCorners {
  0% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
}
</style>
