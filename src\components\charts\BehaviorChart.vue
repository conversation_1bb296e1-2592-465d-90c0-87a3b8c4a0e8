<template>
    <div class="progress-container">
        <div class="chart-section">
            <CarbonSourcePieChart />
        </div>
        <div class="trend-chart-section">
            <CarbonTrendChart />
        </div>
        
    </div>
</template>

<script setup>
import CarbonSourcePieChart from './CarbonSourcePieChart.vue'
import CarbonTrendChart from './CarbonTrendChart.vue'
</script>

<style lang="less" scoped>
.progress-container {
  
    font-family: 'Source Han Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    
    .trend-chart-section {
        width: 100%;
        height:190px;
        margin-bottom: 14px;
  
        border-radius: 8px;
     
    }
    
    .chart-section {
        // 饼图部分保持原样
    }
}
</style>