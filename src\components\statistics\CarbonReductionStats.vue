<template>
    <div class="energy-stats-container">
      <div class="left-section">
        <div class="energy-card main-energy-card">
          <div class="icon-container">
            <svg-icon icon-class="nenghao" class="carbon-icon" />
          </div>
          <div class="data">
            <div class="value">2.5</div>
            <div class="label">建筑能耗总量(tce)</div>
          </div>
        </div>
      </div>
  
      <div class="right-section">
        <!-- 第一行 -->
        <div class="rows">
          <div class="energy-cards">
            <div class="value">18,563</div>
            <div class="label">电力能耗(kWh)</div>
          </div>

          <div class="energy-cards">
            <div class="value">535</div>
            <div class="label">光伏发电量(kWh)</div>
          </div>
        </div>

        <!-- 分隔符 -->
        <div class="divider"></div>

        <!-- 第二行 -->
        <div class="rows">
          <div class="energy-cards">
            <div class="value">18</div>
            <div class="label">用水量(t)</div>
          </div>

          <div class="energy-cards">
            <div class="value">124</div>
            <div class="label">充电桩充电量(kWh)</div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  </script>
  
  <style lang="less" scoped>
  .energy-stats-container {
    display: flex;
    flex-direction: row;
    gap: 16px;
    width: 100%;
    font-family: system-ui, sans-serif;
  }
  
  .left-section {
    height: 160px;
    flex: 1;
    display: flex;
    box-sizing: border-box;
    padding: 4px;
    background: rgba(216, 216, 216, 0.05);
    border-width: 1px;
    border-style: solid;
    border-color: initial;
    border-image: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.2) 2%,
        rgba(255, 255, 255, 0) 34%,
        rgba(255, 255, 255, 0) 71%,
        rgba(255, 255, 255, 0.2) 101%
      )
      1 / 1 / 0 stretch;
  }
  
  .right-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
  
    .rows {
      display: grid;
      grid-template-columns: 1fr 1fr;
      .energy-cards {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        gap: 10px;
        border-radius: 4px;

        .value {
          font-family: D-DIN;
          font-weight: 700;
          font-size: 28px;
          line-height: 26px;
          color: rgb(255, 229, 168);
          text-shadow: rgba(255, 131, 0, 0.5) 0px 1px 16px;
          transition: all 0.3s ease;
        }
        .label {
          font-family: "Source Han Sans";
          font-weight: 600;
          font-size: 13px;
          line-height: 17px;
          color: rgb(255, 255, 255);
          text-align: left;
          font-style: normal;
          text-transform: none;
          transition: all 0.3s ease;
        }
      }
    }
  }
  
  .divider {
    height: 1px;
    width: 100%;
    background: linear-gradient(
      90deg,
      rgba(216, 216, 216, 0),
      rgb(216, 216, 216) 52%,
      rgba(216, 216, 216, 0)
    );
    margin: 16px 0;
  }
  
  .energy-card {
    background-color: #1e283a;
    border-radius: 8px;
  
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background: rgba(216, 216, 216, 0.05);
    border-width: 1px;
    border-style: solid;
    border-color: initial;
    border-image: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.2) 2%,
        rgba(255, 255, 255, 0) 34%,
        rgba(255, 255, 255, 0) 71%,
        rgba(255, 255, 255, 0.2) 101%
      )
      1 / 1 / 0 stretch;
    .data {
      .value {
        display: flex;
  
        justify-content: center;
        align-items: center;
      }
    }
  
    &.main-energy-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
  
      height: 100%;
    }
  
    .value {
      font-family: SourceHanSans;
      font-size: 20px;
      font-weight: bold;
      line-height: normal;
      letter-spacing: 0em;
      text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
      font-variation-settings: "opsz" auto;
      color: #ffe5a8;
      margin-bottom: 4px;
    }
  
    .label {
      font-size: 14px;
      color: #ffffff;
    }
  }
  
  .icon-container {
    position: relative;
    width: 39px;
    height: 41px;
    display: flex;
    align-items: center;
    justify-content: center;

    // 四角扫描框效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 100% 1px,
        linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 100% / 100% 1px,
        linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 1px 100%,
        linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 100% 0 / 1px 100%;
      background-repeat: no-repeat;
      animation: scanCorners 3s ease-in-out infinite;
      pointer-events: none;
    }

    .carbon-icon {
      width: 23px;
      height: 19px;
      z-index: 1;
    }
  }
  
// 扫描框动画
@keyframes scanCorners {
  0% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
}
  </style>