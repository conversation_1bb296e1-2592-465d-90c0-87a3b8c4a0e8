<template>
  <div class="carbon-emission-stats">
    <div class="emission-grid">
      <div class="emission-item" v-for="(item, index) in carbonEmissionItems" :key="index">
        <!-- 上半部分：图标和数字 -->
        <div class="top-section">
          <div class="icon-container">
            <svg-icon :icon-class="item.iconClass" :class="item.iconClassType" />
          </div>
          <div class="emission-value">{{ item.value }}</div>
        </div>
        <!-- 下半部分：文字说明 -->
        <div class="bottom-section">
          <div class="emission-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch, computed } from 'vue'


const props = defineProps({
    timeDimension: {
        type: String,
        default: 'day'
    }
})

// 不同时间维度的数据
const emissionData = {
  day: [
    { iconClass: 'tpf1', iconClassType: 'carbon-icon-1', value: '10.5', label: '建筑碳排放总量(kgCO₂e)' },
    { iconClass: 'tpf2', iconClassType: 'carbon-icon-2', value: '10.5', label: '建筑碳排放强度(kgCO₂e/m²)' },
    { iconClass: 'tpf3', iconClassType: 'carbon-icon-3', value: '10.5', label: '电力碳排放量(kgCO₂e)' },
    { iconClass: 'tpf1', iconClassType: 'carbon-icon-4', value: '10.5', label: '其他碳排放量(kgCO₂e)' }
  ],
  month: [
    { iconClass: 'tpf1', iconClassType: 'carbon-icon-1', value: '315.2', label: '建筑碳排放总量(kgCO₂e)' },
    { iconClass: 'tpf2', iconClassType: 'carbon-icon-2', value: '20.2', label: '建筑碳排放强度(kgCO₂e/m²)' },
    { iconClass: 'tpf3', iconClassType: 'carbon-icon-3', value: '285.8', label: '电力碳排放量(kgCO₂e)' },
    { iconClass: 'tpf1', iconClassType: 'carbon-icon-4', value: '18.6', label: '其他碳排放量(kgCO₂e)' }
  ],
  year: [
  { iconClass: 'tpf1', iconClassType: 'carbon-icon-1', value: '315.2', label: '建筑碳排放总量(kgCO₂e)' },
    { iconClass: 'tpf2', iconClassType: 'carbon-icon-2', value: '20.2', label: '建筑碳排放强度(kgCO₂e/m²)' },
    { iconClass: 'tpf3', iconClassType: 'carbon-icon-3', value: '285.8', label: '电力碳排放量(kgCO₂e)' },
    { iconClass: 'tpf1', iconClassType: 'carbon-icon-4', value: '18.6', label: '其他碳排放量(kgCO₂e)' }
  ]
}

// 根据时间维度计算当前显示的数据
const carbonEmissionItems = computed(() => {
  return emissionData[props.timeDimension] || emissionData.day
})

// 监听时间维度变化
watch(() => props.timeDimension, (newDimension) => {
    console.log('碳排放统计时间维度变化:', newDimension)
}, { immediate: true })


</script>

<style lang="less" scoped>
.carbon-emission-stats {
  .emission-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 5px;

    .emission-item {
      display: flex;
      flex-direction: column;
      background-color: #d8d8d80d;
      padding: 8px 10px;
      border-radius: 4px;
   

      // 上半部分：图标和数字
      .top-section {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .icon-container {
          position: relative;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;

          // 四角扫描框效果
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
              linear-gradient(90deg, #ffffff 0%, #ffffff 8px, transparent 8px, transparent calc(100% - 8px), #ffffff calc(100% - 8px)) 0 0 / 100% 1px,
              linear-gradient(90deg, #ffffff 0%, #ffffff 8px, transparent 8px, transparent calc(100% - 8px), #ffffff calc(100% - 8px)) 0 100% / 100% 1px,
              linear-gradient(0deg, #ffffff 0%, #ffffff 8px, transparent 8px, transparent calc(100% - 8px), #ffffff calc(100% - 8px)) 0 0 / 1px 100%,
              linear-gradient(0deg, #ffffff 0%, #ffffff 8px, transparent 8px, transparent calc(100% - 8px), #ffffff calc(100% - 8px)) 100% 0 / 1px 100%;
            background-repeat: no-repeat;
            animation: scanCorners 3s ease-in-out infinite;
            pointer-events: none;
          }

          .carbon-icon-1,
          .carbon-icon-2,
          .carbon-icon-3,
          .carbon-icon-4,
          .carbon-icon-5 {
            width: 16px;
            height: 14px;
            z-index: 1;
          }
        }

        .emission-value {
          font-family: D-DIN;
          font-size: 20px;
          font-weight: bold;
          color: #FFE5A8;
          text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
          transition: all 0.3s ease;
        }
      }

      // 下半部分：文字说明
      .bottom-section {
        margin-top: 6px;

        .emission-label {
          font-family: SourceHanSans;
          font-size: 12px;
          font-weight: bold;
          color: #ffffff;
          opacity: 0.7;
          line-height: 1.2;
          transition: all 0.3s ease;
        }
      }
    }
  }
}

// 扫描框动画
@keyframes scanCorners {
  0% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
}
</style>