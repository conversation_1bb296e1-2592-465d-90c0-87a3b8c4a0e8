<template>
    <div class="chart-container panel">

        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'


const chartRef = ref(null)
let chart = null

const initChart = () => {
    if (chartRef.value) {
        chart = echarts.init(chartRef.value)
        const option = {
            grid: {
                top: '10%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                axisLine: {
                    lineStyle: {
                        color: '#4B96FF'
                    }
                },
                axisLabel: {
                    color: '#fff'
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#4B96FF'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(75, 150, 255, 0.1)'
                    }
                },
                axisLabel: {
                    color: '#fff'
                }
            },
            series: [
                {
                    data: [18, 15, 20, 25, 22, 28, 30, 25, 22, 20, 18, 15],
                    type: 'bar',
                    barWidth: '40%',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4B96FF' },
                            { offset: 1, color: 'rgba(75, 150, 255, 0.1)' }
                        ])
                    }
                }
            ]
        }
        chart.setOption(option)
    }
}

const handleResize = () => {
    chart?.resize()
}

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    chart?.dispose()
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-container {
    .chart {
        height: 100%;
        width: 100%;
    }
}
</style>