<template>
    <div class="login-page">
        <div class="container" :class="{ active: isSignUp }">
            <div class="form-container sign-up">
                <form @submit.prevent>
                    <h1>创建账号</h1>
                    <div class="social-icons">
                        <a href="#" class="icon" title="微信登录">
                            <el-icon>
                                <ChatDotRound />
                            </el-icon>
                            <span class="login-type" @click="handleWechatLogin">微信123131</span>
                        </a>
                        <a href="#" class="icon" title="QQ登录">
                            <el-icon>
                                <ChatRound />
                            </el-icon>
                            <span class="login-type">QQ</span>
                        </a>
                        <a href="#" class="icon" title="手机登录">
                            <el-icon>
                                <Iphone />
                            </el-icon>
                            <span class="login-type">手机</span>
                        </a>
                        <a href="#" class="icon" title="短信登录">
                            <el-icon>
                                <Message />
                            </el-icon>
                            <span class="login-type">短信</span>
                        </a>
                    </div>
                    <span>或使用邮箱注册</span>
                    <el-input v-model="signUpForm.name" placeholder="姓名" class="form-input" />
                    <el-input v-model="signUpForm.email" placeholder="邮箱" class="form-input" />
                    <el-input v-model="signUpForm.password" type="password" placeholder="密码" show-password
                        class="form-input" />
                    <el-button @click="handleSignUp">注册</el-button>
                </form>
            </div>
            <div class="form-container sign-in">
                <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" @submit.prevent>
                    <div class="title-box">
                        <h1>登录</h1>
                    </div>
                    <div class="social-icons">
                        <a href="#" class="icon" title="微信登录">
                            <el-icon>
                                <ChatDotRound />
                            </el-icon>
                            <span class="login-type">微信</span>
                        </a>
                        <a href="#" class="icon" title="QQ登录">
                            <el-icon>
                                <ChatRound />
                            </el-icon>
                            <span class="login-type">QQ</span>
                        </a>
                        <a href="#" class="icon" title="手机登录">
                            <el-icon>
                                <Iphone />
                            </el-icon>
                            <span class="login-type">手机</span>
                        </a>
                        <a href="#" class="icon" title="短信登录">
                            <el-icon>
                                <Message />
                            </el-icon>
                            <span class="login-type">短信</span>
                        </a>
                    </div>
                    <span>或使用账号密码登录</span>
                    <el-form-item prop="username" class="form-item">
                        <el-input v-model="loginForm.username" placeholder="用户名" :prefix-icon="User"
                            class="form-input" />
                    </el-form-item>
                    <el-form-item prop="password" class="form-item">
                        <el-input v-model="loginForm.password" type="password" placeholder="密码" show-password
                            :prefix-icon="Lock" class="form-input" />
                    </el-form-item>
                    <a href="#">忘记密码？</a>
                    <el-button :loading="loading" @click="handleLogin">登录</el-button>
                </el-form>
            </div>
            <div class="toggle-container">
                <div class="toggle">
                    <div class="toggle-panel toggle-left">
                        <h1 style="color: #fff;">欢迎回来！</h1>
                        <p>请登录您的账号以使用所有功能</p>
                        <el-button class="hidden" @click="toggleForm(false)">登录</el-button>
                    </div>
                    <div class="toggle-panel toggle-right">
                        <h1 style="color: #fff;">你好，朋友！</h1>
                        <p>注册账号以使用所有功能</p>
                        <el-button class="hidden" @click="toggleForm(true)">注册</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
    User,
    Lock,
    ChatDotRound,  // 微信
    ChatRound,     // QQ
    Iphone,        // 手机
    Message        // 短信
} from '@element-plus/icons-vue'
import { login } from '@/api/login'

const route = useRoute()
const router = useRouter()
const loginFormRef = ref(null)
const loading = ref(false)
const isSignUp = ref(false)

const loginForm = ref({
    username: 'admin',
    password: 'CZadmin@2024'
})

const signUpForm = ref({
    name: '',
    email: '',
    password: ''
})

// 登录表单校验规则
const loginRules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应在 3 到 20 个字符之间', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度应在 6 到 20 个字符之间', trigger: 'blur' },
        {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{6,20}$/,
            message: '密码必须包含大小写字母和数字',
            trigger: 'blur'
        }
    ]
}

const handleWechatLogin = () => {
    console.log('微信登录')
  
}

const handleLogin = async () => {
    if (!loginFormRef.value) return

    try {
        // 表单校验
        await loginFormRef.value.validate()
        loading.value = true

        const { token, userInfo } = await login({
            username: loginForm.value.username,
            password: loginForm.value.password
        })

        ElMessage.success('登录成功')
        const redirect = route.query.redirect || '/'
        router.push(redirect)
    } catch (error) {
        if (error.message.includes('validate')) {
            // 表单校验错误
            return
        }
        console.error('登录失败：', error)
        ElMessage.error(error.message || '登录失败')
    } finally {
        loading.value = false
    }
}

const handleSignUp = () => {
    ElMessage.info('注册功能开发中')
}

const toggleForm = (value) => {
    isSignUp.value = value
}
</script>

<style lang="less" scoped>
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

.login-page {
    background-color: #c9d6ff;
    background: linear-gradient(to right, #e2e2e2, #c9d6ff);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100vh;
    padding-bottom: 100px;
}

.container {
    background-color: #fff;
    border-radius: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.35);
    position: relative;
    overflow: hidden;
    width: 768px;
    max-width: 100%;
    min-height: 450px;

    p {
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0.3px;
        margin: 20px 0;
    }

    span {
        font-size: 12px;
    }

    a {
        color: #333;
        font-size: 13px;
        text-decoration: none;
        margin: 15px 0 10px;
    }

    :deep(.el-button) {
        background-color: #512da8;
        color: #fff;
        font-size: 12px;
        padding: 10px 45px;
        border: 1px solid transparent;
        border-radius: 8px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        margin-top: 10px;
        cursor: pointer;

        &.hidden {
            background-color: transparent;
            border-color: #fff;
            color: #fff;
        }
    }

    form,
    :deep(.el-form) {
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding: 20px 40px;
        height: 100%;
        padding-top: 0;
        width: 100%;

        .form-input {
            margin-bottom: 15px;
        }

        .form-input:last-of-type {
            margin-bottom: 0;
        }
    }

    .title-box {
        width: 100%;
        text-align: center;
        // margin-bottom: 20px;

    }

    h1 {
        font-size: 28px;
        color: #333;
        margin-bottom: 20px;
        font-weight: 600;
    }
}

.form-container {
    position: absolute;
    top: 0;
    height: 100%;
    transition: all 0.6s ease-in-out;
}

.sign-in {
    left: 0;
    width: 50%;
    z-index: 2;
}

.container.active .sign-in {
    transform: translateX(100%);
}

.sign-up {
    left: 0;
    width: 50%;
    opacity: 0;
    z-index: 1;
}

.container.active .sign-up {
    transform: translateX(100%);
    opacity: 1;
    z-index: 5;
    animation: move 0.6s;
}

@keyframes move {

    0%,
    49.99% {
        opacity: 0;
        z-index: 1;
    }

    50%,
    100% {
        opacity: 1;
        z-index: 5;
    }
}

.social-icons {
    margin: 10px 0;
    display: flex;
    gap: 15px;

    a {
        border: 1px solid #ddd;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 8px;
        width: 60px;
        height: 50px;
        transition: all 0.3s;
        text-decoration: none;

        &:hover {
            background: #f5f5f5;
            transform: translateY(-2px);
            border-color: #512da8;

            .el-icon {
                color: #512da8;
            }

            .login-type {
                color: #512da8;
            }
        }

        .el-icon {
            font-size: 24px;
            color: #666;
            margin-bottom: 4px;
        }

        .login-type {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
    }
}

.toggle-container {
    position: absolute;
    top: 0;
    left: 50%;
    width: 50%;
    height: 100%;
    overflow: hidden;
    transition: all 0.6s ease-in-out;
    border-radius: 150px 0 0 100px;
    z-index: 1000;
}

.container.active .toggle-container {
    transform: translateX(-100%);
    border-radius: 0 150px 100px 0;
}

.toggle {
    background-color: #512da8;
    height: 100%;
    background: linear-gradient(to right, #5c6bc0, #512da8);
    color: #fff;
    position: relative;
    left: -100%;
    height: 100%;
    width: 200%;
    transform: translateX(0);
    transition: all 0.6s ease-in-out;
}

.container.active .toggle {
    transform: translateX(50%);
}

.toggle-panel {
    position: absolute;
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 30px;
    text-align: center;
    top: 0;
    transform: translateX(0);
    transition: all 0.6s ease-in-out;
}

.toggle-left {
    transform: translateX(-200%);
}

.container.active .toggle-left {
    transform: translateX(0);
}

.toggle-right {
    right: 0;
    transform: translateX(0);
}

.container.active .toggle-right {
    transform: translateX(200%);
}

:deep(.form-item) {
    width: 100%;
    margin-bottom: 15px;
}

:deep(.form-input) {
    width: 100%;

    .el-input__wrapper {
        background-color: #eee;
        border: none;
        padding: 10px 15px;
        height: 42px;
        border-radius: 8px;
        box-shadow: none !important;
    }
}

:deep(.el-form-item__error) {
    color: #ff4949;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 2px;
}

:deep(.el-input__wrapper.is-error) {
    box-shadow: 0 0 0 1px #ff4949 inset !important;
    background-color: #fff2f2;
}
</style>