<template>
    <div class="statistics-container">
        <div class="statistics-card" v-for="(item, index) in statistics" :key="index">
            <div class="card-icon">
                <el-icon :size="14" :color="item.color">
                    <component :is="item.icon" />
                </el-icon>
            </div>
            <div class="card-content">
                <div class="card-value">{{ item.value }}</div>
                <div class="card-label">{{ item.label }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { Monitor, DataLine, Connection } from '@element-plus/icons-vue'

const statistics = ref([
    {
        icon: Monitor,
        value: '26',
        label: '温度(℃)',
        color: '#4B96FF'
    },
    {
        icon: DataLine,
        value: '50',
        label: '湿度(%)',
        color: '#4BFFFC'
    }, {
        icon: DataLine,
        value: '20.3',
        label: '空调颗粒物(mg/m³)',
        color: '#4BFFFC'
    }, {
        icon: DataLine,
        value: '20.3',
        label: '空调颗粒物浓度(mg/m³)',
        color: '#4BFFFC'
    },

])
</script>

<style scoped>
.statistics-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px;

}

.statistics-card {
    background: rgba(216,
            216,
            216, 0.5);
    border-radius: 4px;
    padding: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-icon {
    background: rgba(75, 150, 255, 0.1);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-content {
    flex-grow: 1;
}

.card-value {
    font-size: 24px;
    font-weight: bold;
    color: #FFE5A8;
    margin-bottom: 5px;
}

.card-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);

    /* 降低字体之间的宽度间隙 */
    letter-spacing: -2px;

}
</style>
