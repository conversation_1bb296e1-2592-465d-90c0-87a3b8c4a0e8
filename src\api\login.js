import request from '@/utils/request'
import { encryptData, getPublicKey } from '@/utils/encrypt'
import store from '@/store'

/**
 * 用户登录
 * @param {Object} data
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @returns {Promise<Object>} 登录结果
 */
export async function login(data) {
    try {
        console.log('开始登录流程，用户名:', data.username)
        
        // 获取公钥
        const publicKey = await getPublicKey()
        console.log('获取到公钥')

        // 加密密码
        const encryptedPassword = encryptData(publicKey, data.password)
        console.log('密码加密完成:', encryptedPassword)

        // 构建表单数据
        const formData = new FormData()
        formData.append('username', data.username)
        formData.append('password', encryptedPassword)

        console.log('发送登录请求')
        const response = await request({
            url: 'http://*************:9101/CZC/login',
            method: 'post',
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })

        console.log('登录响应:', response)

        // 处理登录成功的情况
        if (response.code === 998) {
            // 存储到 Vuex
            store.dispatch('loginSuccess', response.data)
            
            return {
                token: response.data.details.authorization,
                userInfo: {
                    name: response.data.details.name,
                    roles: response.data.details.roles
                }
            }
        }

        throw new Error(response.message || '用户名或密码错误')
    } catch (error) {
        console.error('登录失败:', error)
        throw error
    }
}

/**
 * 用户登出
 */
export function logout() {
    return request({
        url: '/logout',
        method: 'post'
    })
} 