import { createRouter, createWebHashHistory } from 'vue-router'
// import Login from '../views/Login.vue'
import store from '../store'

const routes = [
    {
        path: '/',
        name: 'Home',
        component: () => import('../views/Home.vue'),
        meta: {
            requiresAuth: false // 修改为不需要验证
        }
    }
]

const router = createRouter({
    history: createWebHashHistory(),
    routes
})

// 简化路由守卫
router.beforeEach((to, from, next) => {
    next()
})

// 添加错误处理
router.onError((error) => {
    console.error('路由错误:', error)
})

export default router 