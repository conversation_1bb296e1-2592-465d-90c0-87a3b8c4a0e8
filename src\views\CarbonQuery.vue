<template>
  <div class="carbon-query page-container">
    <title-component>碳排查询</title-component>
    <div class="content grid-container">
      <div class="query-form panel">
        <el-form :model="form" label-position="top">
          <el-form-item label="时间范围">
            <el-date-picker v-model="form.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item label="建筑">
            <el-select v-model="form.building" placeholder="请选择建筑">
              <el-option label="B-1#" value="B-1#" />
              <el-option label="B-2#" value="B-2#" />
              <el-option label="B-3#" value="B-3#" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <doughnut-chart />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TitleComponent from '../components/common/TitleComponent.vue'
import DoughnutChart from '../components/charts/DoughnutChart.vue'

const form = ref({
  dateRange: [],
  building: ''
})
</script>

<style lang="less" scoped>
.carbon-query {
  .content {
    grid-template-columns: 1fr;
  }
}
</style>