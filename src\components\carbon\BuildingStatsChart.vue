<template>
  <div class="building-stats-chart">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: [
          { value: 39.68, name: '空调', itemStyle: { color: '#FF6B6B' } },
          { value: 37.88, name: '照明设施', itemStyle: { color: '#4D96FF' } },
          { value: 15.88, name: '动力', itemStyle: { color: '#FFD93D' } },
          { value: 5.85, name: '特殊', itemStyle: { color: '#6BCB77' } },
          { value: 0.71, name: '用水', itemStyle: { color: '#4FA1FF' } }
        ],
        label: {
          show: true,
          color: '#fff',
          formatter: '{d}%'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', () => {
    chart?.resize()
  })
})
</script>

<style lang="less" scoped>
.building-stats-chart {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  
  .chart {
    width: 100%;
    height: 300px;
  }
}
</style> 