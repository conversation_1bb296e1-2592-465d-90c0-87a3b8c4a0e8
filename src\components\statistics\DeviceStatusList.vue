<template>
    <div class="page-container">
        <!-- 下拉框表单 -->
        <el-form :model="form" class="form-container">
            <el-form-item label="基础运营消息" :label-width="100">
                <el-select v-model="form.device" placeholder="请选择设备" class="custom-select" popper-class="custom-popper"
                    :teleported="false">
                    <el-option label="30%入住率" value="device1" />
                    <el-option label="50%入住率" value="device2" />
                    <el-option label="70%入住率" value="device3" />
                    <el-option label="90%入住率" value="device4" />
                </el-select>
            </el-form-item>
        </el-form>

        <!-- 表格区域 -->
        <el-table :data="tableData" class="custom-table" size="small">
            <el-table-column prop="index" label="编号" width="50" align="center" />
            <el-table-column prop="measure" label="主要减排措施" />
            <el-table-column label="完成度" align="center">
                <template #default="scope">
                    <el-progress :percentage="scope.row.progress" :show-text="false" :text-inside="true"
                        stroke-width="8" />
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
    device: 'device2'
})

const tableData = ref([
    { index: 1, measure: '空调系统调适', progress: 30 },
    { index: 2, measure: '自然通风与采光', progress: 50 },
    { index: 3, measure: '照明分区控制', progress: 70 },
    { index: 4, measure: '高效节能设备使用', progress: 90 },
    { index: 5, measure: '室内环境控制', progress: 60 }
])
</script>

<style lang="less" scoped>
.page-container {
    padding: 20px;
    background: transparent;
    width: 100%;
}

// 自定义下拉菜单样式
:deep(.form-container) {
    margin-bottom: 20px;

    .el-form-item__label {
        color: white !important;
    }


}

// 添加下拉选项的样式
:deep(.el-select-dropdown) {
    background: linear-gradient(to bottom,
            rgba(0, 21, 41, 0.95),
            rgba(0, 21, 41, 0.85)) !important;
    border: 1px solid;
    border-image: linear-gradient(to bottom,
            rgba(75, 150, 255, 0.8),
            rgba(75, 150, 255, 0.2)) 1 !important;

    .el-select-dropdown__item {
        color: white !important;

        &.hover,
        &:hover,
        &.is-hovering {

            background: linear-gradient(to right,
                    rgba(75, 150, 255, 0.3),
                    rgba(75, 150, 255, 0.1)) !important;
        }

        &.selected {
            background: linear-gradient(to right,
                    rgba(75, 150, 255, 0.4),
                    rgba(75, 150, 255, 0.2)) !important;
            color: #4B96FF !important;
        }
    }
}

/* 自定义表格样式 */
:deep(.custom-table) {
    width: 100%;
    background: transparent !important;

    // 移除表格边框
    &::before {
        display: none;
    }



    .el-table__inner-wrapper {
        background: transparent !important;
    }

    // 表头样式
    .el-table__header {
        border: none;

        tr {
            background: transparent !important;
        }

        th {
            background: rgba(255, 255, 255, 0.3) !important;
            border: none !important;
            color: #fff;
            font-weight: normal;

            &.is-leaf {
                border: none !important;
            }
        }
    }

    // 表体样式
    .el-table__body {
        background: transparent !important;

        tr {
            background: transparent !important;

            &:hover>td {
                background: rgba(255, 255, 255, 0.05) !important;
            }

            td {
                background: transparent !important;
                border: none !important;
                color: #fff;
            }
        }
    }

    // 调整进度条样式
    .el-progress {
        width: 100%;
    }
}
</style>