<template>
   <div class="carbon-progress">
      <CarbonTrendChart />
   </div>
</template>

<script setup>
import { onMounted, onUnmounted, nextTick } from 'vue'
import CarbonTrendChart from '@/components/charts/CarbonTrendChart.vue'

// 监听折叠状态变化，重新调整图表大小
onMounted(async () => {
   await nextTick()
   // 添加一个小延迟，确保动画完成后重新计算图表大小
   window.addEventListener('transitionend', handleTransitionEnd)
})

onUnmounted(() => {
   window.removeEventListener('transitionend', handleTransitionEnd)
})

const handleTransitionEnd = (e) => {
   // 只处理content相关的transition
   if (e.target.classList.contains('content-inner') ||
       e.target.classList.contains('content')) {
      // 触发窗口resize事件，让图表自动调整大小
      window.dispatchEvent(new Event('resize'))
   }
}
</script>

<style lang="less" scoped>
.carbon-progress {
   width: 100%;
   height: 269px; // 设置图表高度
   transition: height 0.3s ease;
}
</style>

