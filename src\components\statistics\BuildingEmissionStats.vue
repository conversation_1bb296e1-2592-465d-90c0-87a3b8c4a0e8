<template>
    <div class="building-emission-stats">
        <div class="emission-list">
            <div class="emission-item" v-for="(item, index) in currentData" :key="index">
                <div class="icon-box">
                    <img class="icon-type" :src="getIconPath(index + 1)" alt="">
                    <img class="icon-bg" src="@/assets/images/beijing.png" alt="">
                </div>
                <div class="value">{{ item.value }}</div>
                <div class="label">{{ item.label }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    dimension: {
        type: String,
        default: 'month'
    }
})

// 本月数据
const monthData = [
    { value: '35%', label: '照明系统' },
    { value: '25%', label: '空调系统' },
    { value: '20%', label: '电梯系统' },
    { value: '15%', label: '办公设备' },
    { value: '5%', label: '其他设备' }
]

// 本年数据
const yearData = [
    { value: '32%', label: '照明系统' },
    { value: '28%', label: '空调系统' },
    { value: '22%', label: '电梯系统' },
    { value: '13%', label: '办公设备' },
    { value: '5%', label: '其他设备' }
]

// 根据维度切换数据
const currentData = computed(() => {
    return props.dimension === 'month' ? monthData : yearData
})

const getIconPath = (index) => {
    return new URL(`../../assets/images/emission${index}.png`, import.meta.url).href
}
</script>

<style lang="less" scoped>
.building-emission-stats {
    padding: 20px 0px;
    color: #fff;

    .emission-list {
        display: flex;
        justify-content: space-between;

        .emission-item {
            width: 73px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .icon-box {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .icon-type {
                    width: auto;
                    height: 21.62px;
                    margin-bottom: -20px;
                    z-index: 2;
                }

                .icon-bg {
                    width: 73px;
                    height: 60px;
                    margin-bottom: -10px;
                    z-index: 1;
                }
            }

            .value {
                font-family: D-DIN;
                font-size: 18px;
                font-weight: bold;
                line-height: normal;
                text-align: center;
                letter-spacing: 0em;
                font-variation-settings: "opsz" auto;
                color: #FFFFFF;
            }

            .label {
                opacity: 0.7;
                font-family: SourceHanSans;
                font-size: 12px;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                letter-spacing: 0em;
                font-variation-settings: "opsz" auto;
                color: #FFFFFF;
            }
        }
    }
}
</style>