<template>
    <div class="carbon-line-chart">
        <div class="chart-header">
            <div class="time-dimension-selector">
                <span
                    v-for="item in timeDimensions"
                    :key="item.value"
                    :class="['dimension-item', { active: currentTimeDimension === item.value }]"
                    @click="handleDimensionChange(item.value)">
                    {{ item.label }}
                </span>
            </div>
        </div>
        <div ref="chartRef" class="chart-container"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let resizeObserver = null

const props = defineProps({
    timeDimension: {
        type: String,
        default: 'month',
        validator: (value) => ['day', 'month', 'year', 'total'].includes(value)
    }
})

const emit = defineEmits(['dimensionChange'])

// 时间维度选项
const timeDimensions = ref([
    { label: '当日', value: 'day' },
    { label: '本月', value: 'month' },
    { label: '本年', value: 'year' },
    { label: '累计', value: 'total' }
])

const currentTimeDimension = ref(props.timeDimension)

// 处理时间维度切换
const handleDimensionChange = (dimension) => {
    currentTimeDimension.value = dimension
    emit('dimensionChange', dimension)
    loadChartData(dimension)
}

// 根据时间维度获取数据
const getChartData = (dimension) => {
    const dataMap = {
        day: {
            xAxis: ['0时', '4时', '8时', '12时', '16时', '20时', '24时'],
            series: [
                { name: '碳减排总量', data: [8500, 7200, 8800, 5800, 8200, 7500, 6200] },
                { name: '碳减排量', data: [3800, 2500, 4200, 1800, 4000, 3200, 2800] },
                { name: '实际碳排放量', data: [6800, 5800, 7200, 4200, 6500, 5800, 4800] }
            ]
        },
        month: {
            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            series: [
                { name: '碳减排总量', data: [9200, 7500, 8800, 5800, 8200, 8800, 7200, 9500, 7800, 7500, 6200, 6000] },
                { name: '碳减排量', data: [3800, 3200, 2500, 2800, 3200, 4200, 1800, 4000, 2200, 3200, 2800, 3000] },
                { name: '实际碳排放量', data: [6800, 5800, 5800, 4200, 6500, 7200, 4200, 6500, 6200, 5800, 4800, 4500] }
            ]
        },
        year: {
            xAxis: ['2020年', '2021年', '2022年', '2023年', '2024年'],
            series: [
                { name: '碳减排总量', data: [85000, 92000, 88000, 95000, 78000] },
                { name: '碳减排量', data: [38000, 42000, 35000, 45000, 32000] },
                { name: '实际碳排放量', data: [68000, 72000, 65000, 75000, 58000] }
            ]
        },
        total: {
            xAxis: ['第一季度', '第二季度', '第三季度', '第四季度'],
            series: [
                { name: '碳减排总量', data: [250000, 280000, 260000, 290000] },
                { name: '碳减排量', data: [120000, 135000, 125000, 140000] },
                { name: '实际碳排放量', data: [180000, 195000, 185000, 205000] }
            ]
        }
    }
    return dataMap[dimension] || dataMap.month
}

// 加载图表数据
const loadChartData = (dimension) => {
    if (!chart) return
    
    const data = getChartData(dimension)
    
    const option = {
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 30, 93, 0.9)',
            borderColor: '#4B96FF',
            textStyle: {
                color: '#fff'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>'
                params.forEach(param => {
                    result += `<span style="color:${param.color}">${param.seriesName}</span>: ${param.value}<br/>`
                })
                return result
            }
        },
        legend: {
            data: ['碳减排总量', '碳减排量', '实际碳排放量'],
            right: '5%',
            top: '2%',
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            itemWidth: 15,
            itemHeight: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.xAxis,
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: false
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        series: [
            {
                name: '碳减排总量',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#4B96FF'
                },
                lineStyle: {
                    width: 2,
                    color: '#4B96FF'
                },
                data: data.series[0].data
            },
            {
                name: '碳减排量',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#FF8C42'
                },
                lineStyle: {
                    width: 2,
                    color: '#FF8C42'
                },
                data: data.series[1].data
            },
            {
                name: '实际碳排放量',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 10,
                itemStyle: {
                    color: '#52C41A',
                    borderWidth: 2,
                    borderColor: '#52C41A'
                },
                lineStyle: {
                    width: 4,
                    color: '#52C41A',
                    shadowColor: '#52C41A',
                    shadowBlur: 8
                },
                areaStyle: {
                    color: 'transparent'
                },
                data: data.series[2].data
            }
        ]
    }
    
    chart.setOption(option)
}

// 初始化图表
const initChart = async () => {
    if (!chartRef.value) return
    
    await nextTick()
    
    if (chart) {
        chart.dispose()
    }
    
    chart = echarts.init(chartRef.value)
    loadChartData(currentTimeDimension.value)
}

// 处理窗口大小变化
const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

// 监听时间维度变化
watch(() => props.timeDimension, (newDimension) => {
    currentTimeDimension.value = newDimension
    loadChartData(newDimension)
})

// 生命周期管理
onMounted(() => {
    initChart()
    
    // 监听窗口大小变化
    resizeObserver = new ResizeObserver(handleResize)
    if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
    }
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }
    if (resizeObserver) {
        resizeObserver.disconnect()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.carbon-line-chart {
    width: 100%;
    height: 100%;

    .chart-header {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 10px 20px;
        background: rgba(6, 27, 47, 0.8);
        backdrop-filter: blur(14px);
        border-radius: 12px 12px 0 0;

        .time-dimension-selector {
            display: flex;
            gap: 16px;

            .dimension-item {
                color: #ffffff;
                opacity: 0.6;
                cursor: pointer;
                transition: all 0.3s;
                font-family: PangMenZhengDao;
                font-size: 14px;
                font-weight: normal;
                padding: 4px 8px;
                border-radius: 4px;

                &:hover {
                    opacity: 0.8;
                    background: rgba(255, 255, 255, 0.1);
                }

                &.active {
                    opacity: 1;
                    background: rgba(11, 255, 215, 0.2);
                    color: #0BFFD7;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -2px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 20px;
                        height: 2px;
                        background: #0BFFD7;
                        border-radius: 1px;
                    }
                }
            }
        }
    }

    .chart-container {
        width: 100%;
        height: 300px;
        background: rgba(6, 27, 47, 0.8);
        backdrop-filter: blur(14px);
        border-radius: 0 0 12px 12px;
        padding: 16px;
    }
}
</style>
