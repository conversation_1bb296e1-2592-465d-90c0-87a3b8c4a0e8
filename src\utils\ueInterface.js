// 创建事件总线用于Vue组件间通信
import { ref } from 'vue'
import { EventEmitter } from 'events'

// 初始化事件总线
const eventBus = new EventEmitter()

// 定义响应式数据
const boxData = ref(null)
const loudongData = ref(null)
// 不再需要单独为每个楼栋创建 ref

// 定义全局接口对象
if (typeof window !== 'undefined') {
    window.ue = window.ue || {}
    window.ue.interface = window.ue.interface || {}

    // 添加 setSliderValue 方法
    window.ue.interface.setSliderValue = function (value) {

        // 根据需求调用相应的处理函数
        handleBuildingData(value)
    }
}

// 处理 box 数据
function handleBoxData(data) {
    console.log('接收到 box 数据:', data)
    boxData.value = data
    eventBus.emit('box-data-update', data)
}

// 处理楼栋数据（使用固定的事件名称）
function handleBuildingData(data) {


    eventBus.emit('building-data-update', data)
}

// 监听 UE 传来的数据
if (typeof window !== 'undefined') {
    const originalUe4 = window.ue4
    window.ue4 = function (data) {
       

        // 根据数据类型调用相应的处理函数
        if (data === 'BOX_DATA') {
            handleBoxData(data)
        } else if (data === 'LOUDONG_1') {
            loudongData.value = data
            eventBus.emit('loudong-data-update', data)
        } else if (data.startsWith('楼')) {
            handleBuildingData(data)
        }

        // 保持原有的 ue4 功能
        if (originalUe4) {
            originalUe4(data)
        }
    }
}

// 导出方法供Vue组件使用
export const useUEInterface = () => {
    return {
        boxData,
        loudongData,
        /**
         * 监听 box 数据更新
         * @param {Function} callback 回调函数
         * @returns {Function} 移除监听的函数
         */
        onBoxDataUpdate: (callback) => {
            eventBus.on('box-data-update', callback)
            return () => eventBus.off('box-data-update', callback)
        },
        /**
         * 监听楼栋数据更新
         * @param {Function} callback 回调函数
         * @returns {Function} 移除监听的函数
         */
        onLoudongDataUpdate: (callback) => {
            eventBus.on('loudong-data-update', callback)
            return () => eventBus.off('loudong-data-update', callback)
        },

        onBuildingDataUpdate: (callback) => {
            eventBus.on('building-data-update', callback)
            return () => eventBus.off('building-data-update', callback)
        }
    }
}