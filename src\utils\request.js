import axios from 'axios'
import { ElMessage } from 'element-plus'
import store from '@/store'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 创建 axios 实例
const service = axios.create({
    baseURL: 'http://47.101.156.19:9101',  // 修改基础URL
    timeout: 15000,
    headers: {
        'Content-Type': 'application/json;charset=utf-8'
    }
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // 从 Vuex 获取 token
        const token = store.getters.getToken
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
        }

        if (config.url.includes('/CZC/login')) {
            delete config.headers['Authorization']
        }

        return config
    },
    error => {
        console.error('请求错误：', error)
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data

        // 登录成功返回998
        if (res.code === 998) {
            return res
        }



        // token失效
        if (res.code === 401) {
            store.dispatch('logout')
            router.push('/login')
            ElMessage.error('登录已过期，请重新登录')
            return Promise.reject(new Error('登录已过期'))
        }

        ElMessage.error(res.message || '请求失败')
        return Promise.reject(new Error(res.message || '请求失败'))
    },
    error => {
        // 忽略被广告拦截器拦截的请求错误
        if (error.message.includes('ERR_BLOCKED_BY_CLIENT')) {
            return
        }

        console.error('响应错误：', error)
        ElMessage.error(error.message || '网络错误')
        return Promise.reject(error)
    }
)

export default service 