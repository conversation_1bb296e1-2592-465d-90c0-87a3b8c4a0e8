<!-- public/index.html -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>南京银行网点碳排放监管平台</title>
</head>

<body>

    <div id="app"></div>

    <!-- 先加载 UE4.js -->
    <script>
        // 先定义全局对象
        // window.ue = window.ue || {};
        // window.ue.interface = window.ue.interface || {};

        // // 添加必要的方法
        // window.ue.interface.setSliderValue = function (value) {
        //     console.log('setSliderValue called with:', value);
        // };
    </script>
    <!-- <script src="./js/UE4.js"></script> -->
    <!-- <script src="./js/UE5.js"></script> -->

    <!-- 然后加载应用代码 -->
    <script type="module" src="./src/main.js"></script>

    <script>
        // window.addEventListener('DOMContentLoaded', function () {
        //     console.log('UE4 interface status:', {
        //         ue: typeof window.ue !== 'undefined',
        //         ue4: typeof window.ue4 !== 'undefined',
        //         setSliderValue: typeof window.ue?.interface?.setSliderValue === 'function'
        //     });
        // });
    </script>

</body>

</html> 