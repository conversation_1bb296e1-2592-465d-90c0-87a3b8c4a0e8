{"name": "vuexmu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/echarts": "^4.9.22", "axios": "^1.7.9", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "element-plus": "^2.9.0", "jsencrypt": "^3.3.2", "mitt": "^3.0.1", "v-scale-screen": "^2.3.0", "vue": "^3.3.11", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@types/node": "^22.10.1", "@vitejs/plugin-vue": "^4.5.2", "fast-glob": "^3.3.2", "less": "^4.2.1", "vite": "^5.0.8", "vite-plugin-svg-icons": "^2.0.1", "vue-router": "4"}}