<template>
    <div class="building-stats-top">
        <!-- 左侧建筑面积 -->
        <div class="stats-box">
            <div class="value">15,593</div>
            <div class="label">建筑面积<br>m²</div>
        </div>

        <!-- 中间层高 -->
        <div class="stats-box">
            <div class="value">52</div>
            <div class="label">建筑层度</div>
        </div>


        <div>

        </div>
    </div>
</template>

<script setup>
const floorData = [
    { level: '1F', power: '236', yearChange: '-' },
    { level: '2F', power: '253', yearChange: '-' },
    { level: '3F', power: '1212', yearChange: '-' },
    { level: '4F', power: '526', yearChange: '-' },
]
</script>

<style lang="less" scoped>
.building-stats-top {
    display: grid;
    grid-template-columns: auto auto 1fr;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;

    .stats-box {
        background: rgba(21, 32, 43, 0.5);
        border-radius: 4px;
        padding: 16px;
        text-align: center;
        min-width: 120px;

        .value {
            font-family: D-DIN;
            font-size: 24px;
            color: #FFE5A8;
            margin-bottom: 8px;
        }

        .label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            line-height: 1.4;
        }
    }

    .floor-stats {
        .table-header {
            display: grid;
            grid-template-columns: 80px 1fr 80px;
            padding: 8px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        .table-body {
            .table-row {
                display: grid;
                grid-template-columns: 80px 1fr 80px;
                padding: 8px;
                font-size: 14px;
                color: #fff;

                &:nth-child(odd) {
                    background: rgba(255, 255, 255, 0.02);
                }
            }
        }
    }
}
</style>