<template>
  <div class="pie-charts-container">
    <!-- 左侧饼图 - 能源类型 -->
    <div class="pie-chart-wrapper">
      <div ref="pieChartRef1" class="pie-chart"></div>
      <div class="unit">碳排放分布(能源类型)</div>
    </div>

    <!-- 右侧饼图 - 用电设备 -->
    <div class="pie-chart-wrapper">
      <div ref="pieChartRef2" class="pie-chart"></div>
      <div class="unit">碳排放分布(用电设备)</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
// 导入图片
// import imagepar from '@/assets/images/imagepar.png'

const pieChartRef1 = ref(null)
const pieChartRef2 = ref(null)
let chart1 = null
let chart2 = null

// 左侧饼图数据 - 能源类型
const pieData1 = ref([
  { value: 194, name: '电力', percentage: '76.88%' },
  { value: 58.2, name: '燃气', percentage: '23.06%' },
  { value: 0.2, name: '水', percentage: '0.08%' }
])

// 右侧饼图数据 - 用电设备
const pieData2 = ref([
  { value: 194, name: '空调新风', percentage: '45.86%' },
  { value: 148, name: '照明插座', percentage: '34.99%' },
  { value: 40, name: '动力用电', percentage: '9.46%' },
  { value: 41, name: '特殊用电', percentage: '9.69%' }
])

// 颜色映射
const colorMap1 = {
  '电力': '#006AE2',
  '燃气': '#FFD500',
  '水': '#FF6A00'
}

const colorMap2 = {
  '空调新风': '#006AE2',
  '照明插座': '#00D4AA',
  '动力用电': '#FFD500',
  '特殊用电': '#FF6A00'
}

// 创建图表配置的通用函数
const createChartOption = (pieData, colorMap, totalValue, centerX = '25%') => {
  return {
    grid: {
      top: 0,
      bottom: 0,
      left: 0,
      right: '45%',
      containLabel: true
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: '7%',
      itemWidth: 3,
      itemHeight: 6,
      itemGap: 8,
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 350,
        lineHeight: 12,
        letterSpacing: 0,
        fontFamily: 'SourceHanSans',
        fontVariationSettings: '"opsz" auto',
        rich: {
          name: {
            width: 50,
            padding: [0, 0, 0, 8],
            color: '#fff',
            fontFamily: 'SourceHanSans',
            fontSize: 12,
            fontWeight: 350,
            lineHeight: 12,
          },
          percentage: {
            width: 50,
            align: 'left',
            color: '#fff',
            fontFamily: 'SourceHanSans',
            fontSize: 10,
            fontWeight: 350,
            lineHeight: 10,
            padding: [13, 0, 0, 8]
          }
        }
      },
      formatter: function (name) {
        const item = pieData.find(item => item.name === name)
        return [
          `{name|${name}}\n`,
          `{percentage|${item.percentage}}`
        ].join('')
      },
      icon: 'rect',
      itemStyle: {
        borderWidth: 0
      },
      data: pieData.map(item => ({
        name: item.name,
        icon: 'rect'
      }))
    },

    series: [{
      type: 'pie',
      radius: ['55%', '60%'],
      center: ['26%', '50%'],
      avoidLabelOverlap: false,
      clockwise: true,
      startAngle: 90,
      itemStyle: {
        borderRadius: 0
      },
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      data: pieData.map((item, index) => ({
        ...item,
        itemStyle: {
          color: colorMap[item.name]
        },
        startAngle: 90 + (index * 95),
        endAngle: 90 + (index * 95) + 85
      }))
    }],
    title: {
      text: totalValue,
      subtext: '总量(kgCO₂e)',
      left: '22%',
      top: '37%',
      textAlign: 'center',
      textStyle: {
        color: '#FFE5A8',
        fontFamily: 'D-DIN',
        fontSize: 18,
        fontWeight: 'bold',
        opacity: 1
      },
      subtextStyle: {
        color: '#fff',
        align: 'center',
        fontSize: 10,
        fontWeight: 'bold',
        fontFamily: 'SourceHanSans'
      }
    }
  }
}

const initCharts = () => {
  // 初始化左侧饼图
  if (chart1) {
    chart1.dispose()
  }
  chart1 = echarts.init(pieChartRef1.value)
  const option1 = createChartOption(pieData1.value, colorMap1, '21,025', '17.2%')
  chart1.setOption(option1)

  // 初始化右侧饼图
  if (chart2) {
    chart2.dispose()
  }
  chart2 = echarts.init(pieChartRef2.value)
  const option2 = createChartOption(pieData2.value, colorMap2, '21,025', '17.2%')
  chart2.setOption(option2)
}

const handleResize = () => {
  chart1 && chart1.resize()
  chart2 && chart2.resize()
}

onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chart1) {
    chart1.dispose()
    chart1 = null
  }
  if (chart2) {
    chart2.dispose()
    chart2 = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.pie-charts-container {
  display: flex;
  width: 100%;
  height: 160px;
  gap: 10px;

}

.pie-chart-wrapper {
  position: relative;
  flex: 1;
  height: 100%;

  .pie-chart {
    width: 100%;
    height: 100%;
  }

  .unit {
    position: absolute;
    top: -10px;
    left: -37px;
    width: 100%;
    font-family: SourceHanSans;
    font-size: 12px;
    font-weight: 350;
    line-height: normal;
    text-align: center;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    color: #FFFFFF;
  }
}
</style>