<template>
    <div class="project-info">
      <div class="title">南京银行南京利源北路支行零碳网点</div>

      <div class="area-info">
        <div class="area-item" v-for="(item, index) in areaItems" :key="index">
          <div class="icon-container">
            <svg-icon :icon-class="item.iconClass" :class="item.iconClassType" />
          </div>
          <div class="content-container">
            <div class="area-value">{{ item.value }}</div>
            <div class="area-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
export default {
  name: 'ProjectOverview',
  data() {
    return {
      areaItems: [
        {
          iconClass: 'mianji',
          iconClassType: 'area-icon',
          value: '1,024.14',
          label: '建筑面积(m2)'
        },
        {
          iconClass: 'pl',
          iconClassType: 'area-icon1',
          value: '零碳建筑',
          label: '建筑评价'
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.project-info {


  text-align: center;

  .title {
    font-family: PangMenZhengDao;
    font-size: 21px;
    font-weight: bold;
    line-height: 1.4;
    color: #ffffff;
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .area-info {
    display: flex;
    justify-content: center;
    gap: 9px;
    margin-top: 16px;

    .area-item {
      display: flex;
      align-items: center;
      gap: 12px;
      background-color: #d8d8d80d;
    width: 198px;
      padding: 15px 12px;

      .icon-container {
        position: relative;
        width: 39px;
        height: 41px;
        display: flex;
        align-items: center;
        justify-content: center;

        // 四角扫描框效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: 
            linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 100% 1px,
            linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 100% / 100% 1px,
            linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 1px 100%,
            linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 100% 0 / 1px 100%;
          background-repeat: no-repeat;
          animation: scanCorners 3s ease-in-out infinite;
          pointer-events: none;
        }

        .area-icon {
          width: 23px;
          height: 19px;
          z-index: 1;
        }
        .area-icon1 {
          width: 23px;
    height: 19px;
          z-index: 1;
        }
      }

      .content-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .area-value {
          font-family: D-DIN;
          font-size: 24px;
          font-weight: bold;
          color: #FFE5A8;
          margin-bottom: 5px;
          text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
        }

        .area-label {
          font-family: SourceHanSans;
          font-size: 12px;
          font-weight: bold;
          color: #ffffff;



opacity: 0.7;
	
        }
      }
    }
  }
}

// 扫描框动画
@keyframes scanCorners {
  0% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
}
</style>
