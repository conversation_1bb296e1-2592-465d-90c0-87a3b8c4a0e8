<template>
    <div class="progress-container">
        <div class="title">建筑碳中和情况</div>
        <div class="progress-header">
            <div class="left-info">
                <div class="value">{{ formattedValue }}</div>
                <div class="label">碳排放总量<br />(kgCO₂e)</div>
            </div>



            <div class="progress-bar">
                <div class="progress">
                    <div class="progress-inner">
                        <div class="left-part" :style="{ width: `${leftPercentage}%` }">
                            <div class="left-tip"></div>
                            <!-- <span class="percentage">{{ leftPercentage.toFixed(2) }}</span> -->
                        </div>
                        <div class="right-part" :style="{ width: `${rightPercentage}%` }">
                            <!-- <span class="percentage">{{ rightPercentage.toFixed(2) }}</span> -->
                            <div class="right-tip"></div>
                        </div>
                    </div>
                </div>

                <img class="node-img" src="../../assets/images/jiedian.png" alt="">

            </div>
            <div class="progress-footer">

            </div>
            <div class="right-info">
                <div class="value">{{ formattedTarget }}</div>  
                <div class="label">碳减排总量<br />(kgCO₂e)</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    value: {
        type: [Number, String],
        default: 558
    },
    target: {
        type: [Number, String],
        default: 528
    }
})

// 格式化数值显示
const formattedValue = computed(() => Number(props.value).toLocaleString())
const formattedTarget = computed(() => Number(props.target).toLocaleString())

// 计算总量
const total = computed(() => Number(props.value) + Number(props.target))

// 计算左侧百分比（碳排放总量占比）
const leftPercentage = computed(() => (Number(props.value) / total.value) * 100)

// 计算右侧百分比（碳减排总量占比）
const rightPercentage = computed(() => (Number(props.target) / total.value) * 100)
</script>

<style lang="less" scoped>
.progress-container {

    padding: 10px 10px;
    background: rgba(6, 27, 47, 0.8);
    backdrop-filter: blur(14px);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        background: linear-gradient(97deg, #FFFFFF 6%, #C3DFFF 108%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .progress-header {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-bottom: 20px;

        .left-info,
        .right-info {
            display: flex;
            flex-direction: column;
            align-items: center;

            .value {
                font-family: D-DIN;
                font-size: 24px;
                font-weight: bold;
                line-height: normal;
                text-align: center;
                letter-spacing: 0em;

                font-variation-settings: "opsz" auto;
                color: #FFFFFF;

                text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
                margin-bottom: 4px;
            }

            .label {
                opacity: 0.7;

                font-family: SourceHanSans;
                font-size: 12px;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                letter-spacing: 0em;

                font-variation-settings: "opsz" auto;
                color: #FFFFFF;
            }
        }


    }

    .progress-bar {
        position: relative;

        .node-img {
            width: 26px;
            height: 31px;
            position: absolute;
            top: 180%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9;
        }


        .progress {
            width: 513px;
            height: 25px;
            margin: 0 auto;
            background: red;
            border-radius: 0;
            overflow: visible;
            position: relative;

            .progress-inner {
                height: 100%;
                display: flex;
                position: relative;

                .left-part {
                    height: 100%;
                    background: linear-gradient(89deg, #FF954B 11%, #FFD500 88%);
                    display: flex;
                    align-items: center;
                    position: relative;

                    .left-tip {
                        position: absolute;
                        left: -5px;
                        top: -5px;
                        width: 5px;
                        height: 140%;
                        background: #FF9F00;
                        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
                    }

                    .percentage {
                        position: absolute;
                        left: 5px;
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                        z-index: 1;
                    }
                }

                .right-part {
                    height: 100%;
                    // background: linear-gradient(90deg, #00FFB3, #00E8B3);
                    background: linear-gradient(90deg, #0FFFBB -13%, #07B963 94%, #07B963 94%);
                    display: flex;
                    align-items: center;
                    position: relative;

                    .percentage {
                        position: absolute;
                        right: 5px;
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                    }

                    .right-tip {
                        position: absolute;
                        right: -5px;
                        width: 5px;
                        height: 36px;
                        background: #00E8B3;
                        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
                    }
                }
            }
        }
    }
}

.progress-footer {
    position: absolute;
    border-radius: 2px;
    opacity: 0;
    background: #D8D8D8;
    width: 70%;
    height: 37px;
    left: 50%;
    transform: translateX(-50%);
    bottom: -25px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}
</style>