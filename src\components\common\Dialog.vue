<template>
    <div v-if="visible">
        <!-- 遮罩层 -->
        <div class="dialog-mask" @click="handleCloseDialog"></div>
        <div class="dialog-container">
            <div class="dialog-content">
                <div class="dialog-header">
                    <div class="dialog-title">{{ title }}</div>
                    <svg-icon icon-class="guanbi" @click="handleCloseDialog" class="icon" id="close-btn" />
                </div>
                <div class="dialog-body">
                    <slot></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:visible'])

const handleCloseDialog = () => {
    emit('update:visible', false)
 
    console.log("关闭弹窗 UE TanChuang Close")



}
</script>

<style lang="less" scoped>
.dialog-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.01);
    z-index: 999;
}

.dialog-container {
    position: absolute;
    top: -19px;
    left: 50%;
    width: 430px;
    height: 960px;
    opacity: 1;
    transform: translate(-50%);
    background: rgba(15, 22, 33, 0.9);

    border: 1px solid rgba(129, 129, 129, 0.2);

    box-shadow: 0px 4px 32px 0px rgba(0, 74, 156, 0.75);

    z-index: 1000;

    .dialog-content {



        width: 100%;
        overflow: auto;
    }

    .dialog-header {


        background-image: url('../../assets/images/titlesimg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 430px;
        height: 52px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        // padding: 16px 45px;
        .dialog-title {
            margin-left: 45px;

            opacity: 1;

            font-family: PangMenZhengDao;
            font-size: 20px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0em;

            font-variation-settings: "opsz" auto;
            background: linear-gradient(97deg, #FFFFFF 6%, #C3DFFF 108%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .icon {
            margin-right: 22px;
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
    }


}
</style>