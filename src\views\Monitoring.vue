<template>
  <div class="monitoring page-container">
    <title-component>状况监测</title-component>
    <div class="content flex-column">
      <div class="charts-container grid-container">
        <bar-chart />
        <line-chart />
      </div>
      <data-table />
    </div>
  </div>
</template>

<script setup>
import TitleComponent from "../components/common/TitleComponent.vue";
import BarChart from "../components/charts/BarChart.vue";
import LineChart from "../components/charts/LineChart.vue";
import DataTable from "../components/statistics/DataTable.vue";
</script>

<style lang="less" scoped>
.monitoring {
  .charts-container {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style> 