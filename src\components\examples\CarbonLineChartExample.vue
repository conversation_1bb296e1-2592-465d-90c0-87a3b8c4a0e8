<template>
    <div class="carbon-line-chart-example">
        <div class="example-title">
            <h3>碳排放折线图示例</h3>
            <p>展示如何使用 CarbonLineChart 组件</p>
        </div>
        
        <!-- 使用 TitleComponent 包装的示例 -->
        <div class="example-section">
            <h4>使用 TitleComponent 包装</h4>
            <TitleComponent 
                title="碳排放统计" 
                subtitle="Carbon Emission Statistics" 
                :dimensions="dimensions"
                v-model="currentDimension">
                <CarbonLineChart 
                    :timeDimension="currentDimension"
                    @dimensionChange="handleDimensionChange" />
            </TitleComponent>
        </div>
        
        <!-- 独立使用的示例 -->
        <div class="example-section">
            <h4>独立使用</h4>
            <CarbonLineChart 
                :timeDimension="independentDimension"
                @dimensionChange="handleIndependentDimensionChange" />
        </div>
        
        <!-- 当前选择的维度信息 -->
        <div class="dimension-info">
            <p>当前选择的维度（TitleComponent）: {{ currentDimension }}</p>
            <p>当前选择的维度（独立组件）: {{ independentDimension }}</p>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import CarbonLineChart from '@/components/charts/CarbonLineChart.vue'
import TitleComponent from '@/components/common/TitleComponent.vue'

// 时间维度选项
const dimensions = [
    { label: '当日', value: 'day' },
    { label: '本月', value: 'month' },
    { label: '本年', value: 'year' },
    { label: '累计', value: 'total' }
]

// 当前选择的维度
const currentDimension = ref('month')
const independentDimension = ref('day')

// 处理维度变化
const handleDimensionChange = (dimension) => {
    console.log('TitleComponent 维度变化:', dimension)
    currentDimension.value = dimension
}

const handleIndependentDimensionChange = (dimension) => {
    console.log('独立组件维度变化:', dimension)
    independentDimension.value = dimension
}
</script>

<style lang="less" scoped>
.carbon-line-chart-example {
    padding: 20px;
    background: rgba(6, 27, 47, 0.5);
    min-height: 100vh;
    
    .example-title {
        margin-bottom: 30px;
        text-align: center;
        
        h3 {
            color: #fff;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }
    }
    
    .example-section {
        margin-bottom: 40px;
        
        h4 {
            color: #fff;
            font-size: 18px;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 3px solid #0BFFD7;
        }
    }
    
    .dimension-info {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        margin-top: 20px;
        
        p {
            color: #fff;
            margin: 5px 0;
            font-size: 14px;
        }
    }
}
</style>
