<template>
    <div class="chart-container">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let resizeObserver = null

const initChart = async () => {
    if (!chartRef.value) return

    await nextTick()

    if (chart) {
        chart.dispose()
    }

    chart = echarts.init(chartRef.value)
    const option = {
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 30, 93, 0.9)',
            borderColor: '#4B96FF',
            textStyle: {
                color: '#fff'
            }
        },
        legend: {
            data: ['基准情景', '建筑情景'],
            right: '5%',
            top: '2%',
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            itemWidth: 15,
            itemHeight: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#fff'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        yAxis: {
            type: 'value',
            min: 12,
            max: 27,
            interval: 3,
            axisLine: {
                show: false
            },
            axisLabel: {
                color: '#fff'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        series: [
            {
                name: '基准情景',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#4B96FF'
                },
                lineStyle: {
                    width: 3
                },
                data: [16, 16.5, 18, 19.5, 21, 22, 23, 24.5, 24, 24, 22.5, 22]
            },
            {
                name: '建筑情景',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#52C41A'
                },
                lineStyle: {
                    width: 3
                },
                data: [14, 15, 16, 16.5, 17, 17, 17, 18, 17, 17.5, 19, 18]
            }
        ]
    }
    chart.setOption(option)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

onMounted(async () => {
    await initChart()

    resizeObserver = new ResizeObserver(() => {
        handleResize()
    })

    if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
    }

    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }

    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }

    window.removeEventListener('resize', handleResize)
})
</script>



<style lang="less" scoped>
.chart-container {
    .chart {
        height: 100%;
        width: 100%;
    }
}
</style>