<template>
    <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle">
            <el-table-column prop="ranking" label="序号" width="60" />
            <el-table-column prop="unit" label="单位名称" width="80" />
            <el-table-column prop="emission" label="本月碳排放强度">
                <template #default="scope">
                    <span :style="{ color: getEmissionColor(scope.row.emission) }">{{ scope.row.emission }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="change" label="较上月变化" width="100">
                <template #default="scope">
                    <div class="change-indicator">
                        <el-icon :color="getChangeColor(scope.row.change)" :size="20">
                            <component :is="getChangeIcon(scope.row.change)" />
                        </el-icon>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { ArrowUp, ArrowDown, Remove } from '@element-plus/icons-vue'

const headerStyle = {
    background: 'var(--bg-hover)',
    color: 'var(--text-primary)',
    borderBottom: '1px solid var(--border-color)'
}

const cellStyle = {
    background: 'transparent',
    color: 'var(--text-primary)',
    borderBottom: '1px solid var(--border-color)',
    textAlign: 'center'
}

const tableData = ref([
    { ranking: 1, unit: 'B-1#', emission: 5.63, change: 'up' },
    { ranking: 2, unit: 'B-2#', emission: 5.94, change: 'neutral' },
    { ranking: 3, unit: 'A-2#', emission: 6.43, change: 'down' },
    { ranking: 4, unit: 'B-5#', emission: 7.12, change: 'down' },
    { ranking: 5, unit: 'B-8#', emission: 7.85, change: 'up' },
    { ranking: 6, unit: 'B-3#', emission: 7.99, change: 'neutral' }
])

const getEmissionColor = (value) => {
    const num = parseFloat(value)
    if (num >= 7.5) return '#ff4d4f'  // Red for high values
    if (num >= 6.5) return '#faad14'  // Yellow for medium values
    return '#52c41a'  // Green for low values
}

const getChangeIcon = (change) => {
    switch (change) {
        case 'up': return ArrowUp
        case 'down': return ArrowDown
        default: return Remove
    }
}

const getChangeColor = (change) => {
    switch (change) {
        case 'up': return '#52c41a'  // Green for up
        case 'down': return '#4B96FF'  // Blue for down
        default: return '#909399'  // Gray for neutral
    }
}
</script>

<style scoped>
.table-container {
    border-radius: 4px;
    padding: 15px;
    color: var(--text-primary);
}

.change-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
}

:deep(.el-table) {
    background: transparent !important;
}

:deep(.el-table__inner-wrapper::before) {
    display: none;
}

:deep(.el-table tr) {
    background: transparent !important;
}

:deep(.el-table th.el-table__cell) {
    background: transparent !important;
}

:deep(.el-table td.el-table__cell) {
    background: transparent !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
    background: var(--bg-hover) !important;
}
</style>