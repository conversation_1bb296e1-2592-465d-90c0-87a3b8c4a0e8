<template>
    <!-- <div class="time-box">
       
        <div class="time-box__header">
            <div class="time-box__button">区域时空能碳图谱</div>
            <div class="time-box__gradient"></div>
        </div>


        <div class="time-box__selection">
            <el-select v-model="timeValue" placeholder="请选择" class="time-box__select" :teleported="false">
                <el-option v-for="item in optionstime" :key="item.value" :label="item.label" :value="item.value"
                    :teleported="false"></el-option>
            </el-select>


            <div class="time-box__month" v-for="(item, index) in timelists" :key="index"
                @click="handleSelectMonth(index)">
                <div class="time-box__month-item">
                    <div :class="isSelected(index) ? 'circle selected' : 'circle'"></div>
                    <span :class="isSelected(index) ? 'text selected' : 'text'">
                        {{ item.name }}
                    </span>
                </div>
            </div>


            <div class="time-box__divider"></div>
        </div>
    </div> -->
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus"; // 引入 ElMessage

// 绑定的年份值
const timeValue = ref("2025");

// 下拉选项
const optionstime = ref([
    { value: "2025", label: "2025" },
    { value: "2026", label: "2026" },
    { value: "2027", label: "2027" },
]);

// 月份列表
const timelists = ref([
    { name: "1月" },
    { name: "2月" },
    { name: "3月" },
    { name: "4月" },
    { name: "5月" },
    { name: "6月" },
    { name: "7月" },
    { name: "8月" },
    { name: "9月" },
    { name: "10月" },
    { name: "11月" },
    { name: "12月" },
]);

// 上一个点击的月份
const lastSelectedMonth = ref(null);

// 选中的月份索引
const selectedMonthIndex = ref(null);

// 允许点击的标志
const canClick = ref(true);

/**
 * 判断当前月份是否被选中
 * @param {Number} index - 月份索引
 * @returns {Boolean}
 */
const isSelected = (index) => {
    return selectedMonthIndex.value === index;
};

/**
 * 处理月份选择
 * @param {Number} index - 选择的月份索引
 */
const handleSelectMonth = (index) => {
    if (!canClick.value) {
        ElMessage({
            message: '请勿频繁点击，请稍后再试。',
            type: 'warning',
        });
        return;
    }

    canClick.value = false;
    setTimeout(() => {
        canClick.value = true;
    }, 1000); // 一秒后允许再次点击

    // 将数字索引转换为字符串
    const monthData = `${index + 1}月`;
    if (lastSelectedMonth.value) {
      


    lastSelectedMonth.value = monthData;

    if (selectedMonthIndex.value === index) {
        selectedMonthIndex.value = null;
    } else {
        selectedMonthIndex.value = index;
    }
};
</script>

<style lang="less" scoped>
.time-box {
    /* 整体容器样式 */
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: absolute;
    top: 80px;
    left: calc(var(--side-panel-width) + var(--side-panel-margin) + 10px);
    z-index: 100;

    &__header {
        /* 区域时空能碳图谱部分 */
        position: relative;
        width: 210px;
        height: 70px;
        background: linear-gradient(91deg, rgb(97 109 121 / 63%) 5%, rgba(0, 103, 202, 0) 120%);
        border-radius: 5px;

        .time-box__button {
            /* 按钮样式 */
            display: flex;
            align-items: center;
            justify-content: center;
            height: 22px;
            font-size: 17px;
            font-family: "YouSheTitle", "YouSheTitle-Normal";
            color: #ffffff;
            padding-left: 13px;
            padding-top: 5px;
            cursor: pointer;
        }

        .time-box__gradient {
            /* 渐变条样式 */
            width: 174px;
            height: 18px;
            background: linear-gradient(270deg,
                    #c74238,
                    #ff0000 7%,
                    #e68d79 17%,
                    #e5dc9e 30%,
                    #cfd5b3 39%,
                    #abc3cf 49%,
                    #71a9fe 56%,
                    #5170d6 76%,
                    #383aad);
            border-radius: 3px;
            margin: 15px 12px 0 12px;
        }
    }

    &__selection {
        /* 时间选择部分 */
        width: 134px;
        background: linear-gradient(91deg, rgb(97 109 121 / 63%) 5%, rgba(0, 103, 202, 0) 120%);
        border-radius: 5px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;

        :deep(.el-select) {
            width: 120px;
            margin-bottom: 15px;

            .el-input__wrapper {
                background: transparent;
                box-shadow: none;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .el-input__inner {
                color: #fff;
            }
        }
    }
}

.time-box__month {
    width: 100%;
    cursor: pointer;
    margin-bottom: 22.3px;

    &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }

    .circle {
        width: 15px;
        height: 15px;
        background: #fff;
        border: 3px solid #555555;
        border-radius: 50%;
        transition: background-color 0.3s, transform 0.3s;
    }

    .circle.selected {
        background: #0347bc;
        transform: scale(1.2);
    }

    .text {
        margin-left: 10px;
        font-size: 14px;
        color: #ffffff;
        transition: color 0.3s, transform 0.3s;
    }

    .text.selected {
        color: rgb(50, 102, 188);
        font-weight: bold;
    }
}

.time-box__divider {
    z-index: -1;
    position: absolute;
    height: 484px;
    align-items: center;
    justify-content: center;
    left: 14.4px;
    width: 5.6px;
    top: 60px;
    background: #ffffff;
}
</style>