<template>
    <div class="app-container">
        <!-- 背景图片层 -->
        <div class="background-layer"></div>

        <router-view @project-change="handleProjectChange"></router-view>
    </div>
</template>

<script setup>
import { ref } from 'vue'


// 当前状态
const currentState = ref({
    project: 'nanjing-bank'
})

// 处理项目切换
const handleProjectChange = (projectId) => {
    console.log('App.vue - 接收到项目切换事件:', projectId)
    currentState.value.project = projectId
}
</script>

<style lang="less">
// App容器样式
.app-container {
    position: absolute;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    // 背景图片层
    .background-layer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        background: url('@/assets/images/beijinzezhao.png') center/cover no-repeat;
        pointer-events: none; // 不阻挡用户交互
    }



    // iframe连接状态指示器
    .connection-status {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 20px;
        color: #fff;
        font-size: 12px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        pointer-events: auto;

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4d4f;
            animation: pulse 2s infinite;
        }

        &.connected .status-dot {
            background: #52c41a;
            animation: none;
        }
    }

    // 路由视图容器
    > * {
        position: relative;
        z-index: 3;
        pointer-events: none; // 默认不阻挡Three.js交互
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

// 全局变量
:root {
    // 主题色
    --primary-color: #4B96FF;
    --primary-hover-color: #69A9FF;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --danger-color: #ff4d4f;

    // 背景色
    --bg-dark: rgba(0, 0, 0, 0);
    --bg-panel: rgba(37, 64, 117, 0.55);
    --bg-hover: rgba(255, 255, 255, 0.1);

    // 文字颜色
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-disabled: rgba(255, 255, 255, 0.4);

    // 边框颜色
    --border-color: rgba(75, 150, 255, 0.1);

    // 渐变色
    --gradient-primary: linear-gradient(to right, #1a3c6e, #1e4b8f);

    // 间距
    --spacing-small: 16px;
    --spacing-base: 15px;
    --spacing-large: 20px;

    // 字体大小
    --font-size-small: 14px;
    --font-size-base: 16px;
    --font-size-large: 18px;
    --font-size-xlarge: 20px;
    --font-size-xxlarge: 24px;
}

.v-screen-box {
    background-color: transparent !important;
    pointer-events: none !important;
}

.el-select__wrapper {
    background: linear-gradient(rgba(99, 242, 242, 0), rgba(32, 87, 140, .627) 85.63%, #006eff) !important;
    box-shadow: none !important;

}

.el-select-dropdown__item.is-hovering {
    background: rgba(75, 150, 255, 0.3) !important;
}

.el-select__placeholder {
    color: #fff !important;
}

// 全局样式重置
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// Box数据样式
.box-data {
    background: var(--bg-panel);
    padding: 15px;
    border-radius: 4px;
    color: var(--text-primary);
    font-family: monospace;
    text-align: center;
    font-size: 16px;
    word-break: break-all;
}

// 对话框样式
:deep(.el-dialog) {
    background: rgba(6, 30, 93, 0.9);
    border: 1px solid var(--primary-color);
    backdrop-filter: blur(10px);

    .el-dialog__header {
        border-bottom: 1px solid var(--border-color);
        margin: 0;
        padding: 15px 20px;

        .el-dialog__title {
            color: var(--text-primary);
        }
    }

    .el-dialog__body {
        color: var(--text-primary);
        padding: 20px;
    }

    .el-dialog__headerbtn {
        .el-dialog__close {
            color: var(--text-primary);
        }
    }
}

// 动画相关
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.3s ease;
}

.slide-fade-enter-from {
    opacity: 0;
    transform: translateX(20px);
}

.slide-fade-leave-to {
    opacity: 0;
    transform: translateX(-20px);
}

.scale-fade-enter-active,
.scale-fade-leave-active {
    transition: all 0.3s ease;
}

.scale-fade-enter-from,
.scale-fade-leave-to {
    opacity: 0;
    transform: scale(0.9);
}

// 添加动画类
.animate-hover {
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

.animate-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

// 添加页面切换动画
.page-enter-active,
.page-leave-active {
    transition: all 0.3s ease;
}

.page-enter-from {
    opacity: 0;
    transform: translateX(30px);
}

.page-leave-to {
    opacity: 0;
    transform: translateX(-30px);
}

// 添加卡片悬浮效果
.hover-card {
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }
}

// 添加渐变动画
.gradient-animate {
    background-size: 200% 200%;
    animation: gradient 15s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}
</style>