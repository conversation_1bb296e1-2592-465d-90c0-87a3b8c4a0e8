<template>
    <div class="pie-chart-wrapper">
        <div ref="pieChartRef" class="pie-chart"></div>

    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const pieChartRef = ref(null)
let chart = null

const props = defineProps({
    dimension: {
        type: String,
        default: 'day'
    },

})

// 基础数据
const dataMap = {
    day: [
        { value: 49, name: '低碳步行' },
        { value: 234, name: '在线会议' },
        { value: 31, name: '氢能自行车' },
        { value: 221, name: '充电桩' },
    ],
    month: [
        { value: 477, name: '低碳步行' },
        { value: 2461, name: '在线会议' },
        { value: 329, name: '氢能自行车' },
        { value: 2016, name: '充电桩' },
    ],
    year: [
        { value: 477, name: '低碳步行' },
        { value: 2461, name: '在线会议' },
        { value: 329, name: '氢能自行车' },
        { value: 2016, name: '充电桩' },
    ]
}

// 根据不同时间维度获取数据
const getBuildingData = (dimension) => {
    return dataMap[dimension] || dataMap.day;
}

// 更新数据结构
const pieData = ref(getBuildingData(props.dimension))

// 监听维度变化
watch(() => props.dimension, (newDimension) => {
    console.log('维度变化:', newDimension);
    pieData.value = getBuildingData(newDimension);
    initChart();
}, { immediate: true })

// 移除监听器，因为不再需要切换数据
const colorMap = {
    '低碳步行': '#006AE2',
    '在线会议': '#83BDFF',
    '氢能自行车': '#78FFB7',
    '充电桩': '#FFD500'
}

const initChart = () => {
    if (chart) {
        chart.dispose()
    }

    chart = echarts.init(pieChartRef.value)
    const option = {
        grid: {
            top: 0,
            bottom: 0,
            left: 0,
            right: '55%',
            containLabel: true
        },
        legend: {
            orient: 'vertical',
            right: '0%',
            top: 'middle',
            itemWidth: 3,
            itemHeight: 16,
            itemGap: 20,
            textStyle: {
                color: '#fff',
                fontSize: 16,
                fontWeight: 350,
                lineHeight: 16,
                letterSpacing: 0,
                fontFamily: 'SourceHanSans',
                fontVariationSettings: '"opsz" auto',
                rich: {
                    name: {
                        width: 60,
                        padding: [0, 0, 0, 10],
                        color: '#fff',
                        fontFamily: 'SourceHanSans',
                        fontSize: 16,
                        fontWeight: 350,
                        lineHeight: 16,
                    },
                    value: {
                        width: 100,
                        align: 'right',
                        color: '#fff',
                        fontFamily: 'SourceHanSans',
                        fontSize: 16,
                        fontWeight: 350,
                        lineHeight: 16,
                    },


                }
            },
            formatter: function (name) {
                const item = pieData.value.find(item => item.name === name)
                return [
                    `{name|${name}}`,
                    `{value|${item.value.toLocaleString()}}`
                ].join('')
            },
            icon: 'rect',
            itemStyle: {
                borderWidth: 0
            },
            data: pieData.value.map(item => ({
                name: item.name,
                icon: 'rect'
            }))
        },

        series: [{
            type: 'pie',
            radius: ['62%', '68%'],
            center: ['24.2%', '50.9%'],
            avoidLabelOverlap: false,
            clockwise: true,
            startAngle: 90,
            itemStyle: {
                borderRadius: 0
            },
            label: {
                show: false
            },
            labelLine: {
                show: false
            },
            data: pieData.value.map((item, index) => ({
                ...item,
                itemStyle: {
                    color: colorMap[item.name]
                },
                startAngle: 90 + (index * 95),
                endAngle: 90 + (index * 95) + 85
            }))
        }],
        title: {
            text: Math.round(pieData.value.reduce((sum, item) => sum + item.value, 0)).toLocaleString(),
            subtext: 'kgCO₂e',
            left: '23%',
            top: '35.5%',
            textAlign: 'center',
            textStyle: {
                color: '#FFE5A8',
                fontFamily: 'D-DIN',
                fontSize: 16,
                fontWeight: 'bold',
                opacity: 1
            },
            subtextStyle: {
                color: '#fff',
                align: 'center',
                fontSize: 13,
                fontWeight: 'bold',
                fontFamily: 'SourceHanSans'
            }
        }
    }

    chart.setOption(option)
}

const handleResize = () => {
    chart && chart.resize()
}

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.pie-chart-wrapper {
    position: relative;
    width: 100%;
    height: 172px;


    .pie-chart {
        width: 100%;
        height: 100%;
    }

    .unit {
        position: absolute;
        top: 0px;
        left: -160px;
        width: 100%;

        font-family: SourceHanSans;
        font-size: 16px;
        font-weight: 350;
        line-height: normal;
        text-align: center;
        letter-spacing: 0em;
        font-variation-settings: "opsz" auto;
        color: #FFFFFF;


    }
}
</style>