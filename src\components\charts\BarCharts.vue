<template>
    <div class="chart-container panel">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null

const initChart = () => {
    if (chartRef.value) {
        chart = echarts.init(chartRef.value)
        const option = {
            title: {
                text: '单位：kgCO₂e',
                right: '3%',
                top: '3%',
                textStyle: {
                    color: 'rgba(255, 255, 255, 0.6)',
                    fontSize: 12,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: [{
                    name: '本月减碳量',
                    itemStyle: {
                        color: '#006AE2'
                    }
                }, {
                    name: '本年碳减排',
                    itemStyle: {
                        color: '#0FFFBB'
                    }
                }],
                textStyle: {
                    color: '#fff',
                    fontSize: 12,
                },
                top: 10,
                left: '10',
                icon: 'rect',
                itemWidth: 10.77,
                itemHeight: 7.01,
                itemGap: 16,
                textGap: 8,
                inactiveColor: '#666',
                selectedMode: true,
                selected: {
                    '本月减碳量': true,
                    '本年碳减排': true
                },
                itemStyle: {
                    borderWidth: 0
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['光伏发电', '风力发电', '氢能发电', '绿植碳汇'],
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255, 255, 255, 0.25)'
                    }
                },
                axisLabel: {
                    color: '#fff',
                    fontSize: 12,
                    margin: 12
                }
            },
            yAxis: {
                type: 'value',

                show: true,
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(255, 255, 255, 0.1)',
                        type: 'dashed',
                        width: 0.5
                    }
                },
                axisLabel: {
                    color: '#fff',
                    fontSize: 12,
                    margin: 16
                }
            },
            series: [
                {
                    name: '本月减碳量',
                    type: 'bar',
                    barWidth: '16px',
                    data: [67720, 169, 24, 1375],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#006AE2' },
                            { offset: 1, color: 'rgba(0, 106, 226, 0.1)' }
                        ])
                    }
                },
                {
                    name: '本年碳减排',
                    type: 'bar',
                    barWidth: '16px',
                    data: [67720, 169, 24, 1375],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#0FFFBB' },
                            { offset: 1, color: 'rgba(15, 255, 187, 0.1)' }
                        ])
                    }
                }
            ]
        }
        chart.setOption(option)
    }
}

const handleResize = () => {
    chart?.resize()
}

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    chart?.dispose()
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-container {
    .chart {
        height: 100%;
        width: 100%;
    }

    border: 1px dashed rgba(255, 255, 255, 0.1);
}
</style>