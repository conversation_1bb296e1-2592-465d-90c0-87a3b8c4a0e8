<template>
    <div class="monthly-emission-chart">
        <bar-chart :data="monthlyData" :categories="categories" :series="[
            { name: '碳排放量', color: '#4B96FF' }
        ]" />
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import BarChart from './BarChart.vue'

const monthlyData = ref([
    { month: '1月', '碳排放量': 18 },
    { month: '2月', '碳排放量': 15 },
    { month: '3月', '碳排放量': 20 },
    { month: '4月', '碳排放量': 25 },
    { month: '5月', '碳排放量': 22 },
    { month: '6月', '碳排放量': 28 },
    { month: '7月', '碳排放量': 30 },
    { month: '8月', '碳排放量': 25 },
    { month: '9月', '碳排放量': 22 },
    { month: '10月', '碳排放量': 20 },
    { month: '11月', '碳排放量': 18 },
    { month: '12月', '碳排放量': 15 }
])

const categories = computed(() => monthlyData.value.map(item => item.month))
</script>

<style lang="less" scoped>
.monthly-emission-chart {
    width: 100%;
    height: 320px;
}
</style>