// 检查 'ue' 或 'ue.interface' 是否不是对象
if (typeof ue !== "object" || typeof ue.interface !== "object") {
  // 如果 'ue' 不是对象，则初始化为空对象
  if (typeof ue !== "object") {
    ue = {};
  }

  // 初始化 'ue.interface' 为空对象
  ue.interface = {};

  // 定义 'broadcast' 函数，用于未预定义的情况下发送消息
  ue.interface.broadcast = function (event, data) {
    if (typeof event === "string") {
      var message = [event, ""];
      if (data !== undefined) {
        message[1] = data;
      }
      var encodedMessage = encodeURIComponent(JSON.stringify(message));

      if (typeof history === "object" && typeof history.pushState === "function") {
        history.pushState({}, "", "#" + encodedMessage);
        history.pushState({}, "", "#" + encodeURIComponent(""));
      } else {
        document.location.hash = encodedMessage;
        document.location.hash = encodeURIComponent("");
      }
    }
  };
} else {
  // 当 'ue' 和 'ue.interface' 已存在时，确保 'broadcast' 正确定义

  // 保存现有的 'broadcast' 方法
  var existingBroadcast = ue.interface.broadcast;

  // 检查现有的 'broadcast' 是否为函数
  if (typeof existingBroadcast === "function") {
    // 重新定义 'broadcast'，确保正确序列化数据
    ue.interface.broadcast = function (event, data) {
      if (typeof event === "string") {
        if (data !== undefined) {
          existingBroadcast(event, JSON.stringify(data));
        } else {
          existingBroadcast(event, "");
        }
      }
    };
  } else {
    // 如果 'existingBroadcast' 不是函数，记录错误或定义备用方案


    // 可选：定义一个备用的 'broadcast' 方法，类似于初始化时的情况
    ue.interface.broadcast = function (event, data) {
      if (typeof event === "string") {
        var message = [event, ""];
        if (data !== undefined) {
          message[1] = data;
        }
        var encodedMessage = encodeURIComponent(JSON.stringify(message));

        if (typeof history === "object" && typeof history.pushState === "function") {
          history.pushState({}, "", "#" + encodedMessage);
          history.pushState({}, "", "#" + encodeURIComponent(""));
        } else {
          document.location.hash = encodedMessage;
          document.location.hash = encodeURIComponent("");
        }
      }
    };
  }
}

// 将 'broadcast' 赋值给全局变量，便于访问
window.ue4 = ue.interface.broadcast;

// 确保 'window.ue' 指向 'ue' 对象
window.ue = ue;
