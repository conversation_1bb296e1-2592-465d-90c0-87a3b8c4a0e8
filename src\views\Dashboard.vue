<template>
    <div class="dashboard">
        <BehaviorChart @openDialog="handleOpenDialog" />
        <Dialog v-model:visible="dialogVisible" title="低碳场景详情">
            <div class="dialog-content">
                <!-- 弹窗内容 -->
                <div class="scene-details">
                    <!-- 这里可以放置场景详情的内容 -->
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import BehaviorChart from '../components/charts/BehaviorChart.vue'
import Dialog from '../components/common/Dialog.vue'

const dialogVisible = ref(false)

const handleOpenDialog = () => {
    dialogVisible.value = true
}
</script>

<style lang="less" scoped>
.dashboard {
    position: relative;
    width: 100%;
    height: 100%;
}
</style>