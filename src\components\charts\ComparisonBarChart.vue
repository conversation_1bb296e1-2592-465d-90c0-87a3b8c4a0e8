<template>
    <div class="chart-container">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let resizeObserver = null

const initChart = async () => {
    if (!chartRef.value) return

    // 等待下一个DOM更新周期
    await nextTick()

    // 如果已经存在图表实例，先销毁
    if (chart) {
        chart.dispose()
    }

    // 初始化图表
    chart = echarts.init(chartRef.value)
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            backgroundColor: 'rgba(6, 30, 93, 0.9)',
            borderColor: '#4B96FF',
            textStyle: {
                color: '#fff'
            }
        },
        legend: {
            data: ['引导值', '本年度'],
            right: '5%',
            top: '2%',
            textStyle: {
                color: '#fff'
            },
            itemWidth: 15,
            itemHeight: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['A-1#', 'A-2#', 'B-1#', 'B-2#', 'B-3#', 'B-4#', 'B-5#', 'B-6#', 'B-7#', 'B-8#', 'B-9#', 'B-10#', 'B-11#'],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#fff',
                interval: 0
            }
        },
        yAxis: {
            type: 'value',
            min: 12,
            max: 27,
            interval: 3,
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            axisLine: {
                show: false
            },
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '引导值',
                type: 'bar',
                barWidth: 10,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#4B96FF' },
                        { offset: 1, color: 'rgba(75, 150, 255, 0.1)' }
                    ])
                },
                data: [16, 18, 17, 19, 20, 21, 22, 23, 24, 23, 22, 21, 17]
            },
            {
                name: '本年度',
                type: 'bar',
                barWidth: 10,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#52C41A' },
                        { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
                    ])
                },
                data: [15, 16, 16, 17, 17, 16, 17, 18, 17, 18, 19, 16, 16]
            }
        ]
    }
    chart.setOption(option)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

onMounted(async () => {
    await initChart()

    // 使用 ResizeObserver 监听容器大小变化
    resizeObserver = new ResizeObserver(() => {
        handleResize()
    })

    if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
    }

    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }

    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }

    window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 220px;
    position: relative;
}

.chart {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
</style>