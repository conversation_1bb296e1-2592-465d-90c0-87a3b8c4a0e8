<template>
  <div class="indoor-environment">
    <div class="environment-grid">
      <div class="environment-item" v-for="(item, index) in environmentItems" :key="index">
        <div class="icon-container">
          <svg-icon :icon-class="item.iconClass" :class="item.iconClassType" />
        </div>
        <div class="content-container">
          <div class="environment-value">{{ item.value }}</div>
          <div class="environment-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'IndoorEnvironment',
  data() {
    return {
      environmentItems: [
        {
          iconClass: 'shinei1',
          iconClassType: 'environment-icon-1',
          value: '25',
          label: '室内温度(°C)'
        },
        {
          iconClass: 'shinei2',
          iconClassType: 'environment-icon-2',
          value: '60',
          label: '室内湿度(%)'
        },
        {
          iconClass: 'shinei3',
          iconClassType: 'environment-icon-4',
          value: '450',
          label: 'CO₂(ppm)'
        },
        {
          iconClass: 'shinei4',
          iconClassType: 'environment-icon-4',
          value: '8',
          label: '颗粒物浓度(μg/m³)'
        },
        {
          iconClass: 'shinei5',
          iconClassType: 'environment-icon-5',
          value: '0.02',
          label: 'TVOC(ug/m³)'
        },
        {
          iconClass: 'shinei6',
          iconClassType: 'environment-icon-6',
          value: '0.1',
          label: '甲醛(ug/m³)'
        }
      ]
    }
  },

}
</script>

<style lang="less" scoped>
.indoor-environment {
  .environment-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 12px;
    margin: 10px 0;
    .environment-item {
      display: flex;
      align-items: center;
      gap: 12px;
      background-color: #d8d8d80d;
   
      padding: 11px 12px;

      .icon-container {
        position: relative;
        width: 39px;
        height: 41px;
        display: flex;
        align-items: center;
        justify-content: center;

        // 四角扫描框效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: 
            linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 100% 1px,
            linear-gradient(90deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 100% / 100% 1px,
            linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 0 0 / 1px 100%,
            linear-gradient(0deg, #ffffff 0%, #ffffff 10px, transparent 10px, transparent calc(100% - 10px), #ffffff calc(100% - 10px)) 100% 0 / 1px 100%;
          background-repeat: no-repeat;
          animation: scanCorners 3s ease-in-out infinite;
          pointer-events: none;
        }

        .environment-icon-1,
        .environment-icon-2,
        .environment-icon-3,
        .environment-icon-4,
        .environment-icon-5,
        .environment-icon-6 {
          width: 23px;
          height: 19px;
          z-index: 1;
        }
      }

      .content-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .environment-value {
          font-family: D-DIN;
          font-size: 24px;
          font-weight: bold;
          color: #FFE5A8;
          margin-bottom: 5px;
          text-shadow: 0px 1px 16px rgba(255, 131, 0, 0.5);
          transition: all 0.3s ease;
        }

        .environment-label {
          font-family: SourceHanSans;
          font-size: 13px;
          font-weight: bold;
          color: #ffffff;
          opacity: 0.7;
          transition: all 0.3s ease;
        }
      }
    }
  }
}

// 扫描框动画
@keyframes scanCorners {
  0% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
}
</style>
