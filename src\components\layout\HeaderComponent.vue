<template>
    <div class="header-container">
        <!-- 左侧区域 - 地区和天气 -->
        <div class="header-left">
            <div class="location-weather">
                <span class="city">贵阳市</span>
                <span class="weather-icon">☁️</span>
                <span class="weather-desc">大雨</span>
                <span class="temp-value">17-28°C</span>
             
            </div>
        </div>

        <!-- 中间区域 - 标题 -->
        <div class="header-center">
            <div class="title">零碳数智管理平台</div>
        </div>

        <!-- 右侧区域 - 日期时间 -->
        <div class="header-right">
            <div class="datetime-info">
                <span class="date-value">{{ currentDate }}</span>
                <span class="weekday">{{ currentWeekday }}</span>
                <span class="time-value">{{ currentTime }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')
const currentWeekday = ref('')

// 更新时间
const updateTime = () => {
    const now = new Date()
    
    // 格式化时间 HH:MM:SS
    currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
    
    // 格式化日期 YYYY-MM-DD
    currentDate.value = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    }).replace(/\//g, '-')
    
    // 格式化星期
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    currentWeekday.value = weekdays[now.getDay()]
}

// 定时器
let timeInterval = null

// 监听来自iframe的消息
const handleIframeMessage = (event) => {
    const { type, data } = event.data || {}

    switch (type) {
        case 'FLOOR_SWITCH_COMPLETE':
            console.log('楼层切换完成:', data?.floor)
            break

        case 'FLOOR_SWITCH_ERROR':
            console.error('楼层切换失败:', data?.error)
            break

        case 'MODEL_READY':
            console.log('模型已准备就绪')
            break

        default:
            if (type) {
                console.log('收到未知消息类型:', type, data)
            }
    }
}

onMounted(() => {
    // 初始化时间
    updateTime()
    // 每秒更新时间
    timeInterval = setInterval(updateTime, 1000)
    
    // 添加iframe消息监听器
    window.addEventListener('message', handleIframeMessage)
})

onUnmounted(() => {
    // 清理定时器
    if (timeInterval) {
        clearInterval(timeInterval)
    }
    
    // 清理iframe消息监听器
    window.removeEventListener('message', handleIframeMessage)
})
</script>

<style lang="less" scoped>
.header-container {
    width: 1920px;
    height: 74px;
    background-image: url('@/assets/images/backgroundbgc.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
   
    justify-content: space-between;
    padding: 0 40px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

// 左侧区域 - 地区和天气
.header-left {
    display: flex;
    align-items: flex-start;
    min-width: 400px;
    padding-top:4px;

    .location-weather {
        display: flex;
        align-items: center;
        gap: 12px;

        .city {
            color: #ffffff; // 白色
            font-family: 'PangMenZhengDaoBiaoTiTiMianFeiBan-2', sans-serif;
            font-size: 24px;
            font-weight: 400;
        }

        .weather-icon {
            font-size: 24px;
            color: #45A2FF; // 45A2FF
        }

        .weather-desc {
            color: #45A2FF; // 45A2FF
            font-family: 'PangMenZhengDaoBiaoTiTiMianFeiBan-2', sans-serif;
            font-size: 24px;
            font-weight: 400;
        }

        .temp-value {
            color: #45A2FF; // 45A2FF
            font-family: 'D-DIN', sans-serif;
            font-size: 24px;
            font-weight: bold;
        }

    }
}

// 中间区域 - 标题
.header-center {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    height: 74px;

    .title {
        color: #ffffff;
        font-family: 'PangMenZhengDaoBiaoTiTiMianFeiBan-2', sans-serif;
        font-size: 36px;
        font-weight: bold;
        text-align: center;
        letter-spacing: 2px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
}

// 右侧区域 - 日期时间
.header-right {
    display: flex;
    align-items: flex-start;
    min-width: 400px;
    justify-content: flex-end;
    padding-top: 4px;

    .datetime-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .date-value {
            color: #45A2FF; // 45A2FF
            font-family: 'D-DIN', sans-serif;
            font-size: 24px;
            font-weight: 500;
        }

        .weekday {
            color: #45A2FF; // 45A2FF
            font-family: 'PangMenZhengDaoBiaoTiTiMianFeiBan-2', sans-serif;
            font-size: 24px;
            font-weight: 400;
        }

        .time-value {
            color: #ffffff; // 白色
            font-family: 'D-DIN', sans-serif;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 1px;
        }
    }
}

// 响应式设计
@media (max-width: 1920px) {
    .header-container {
        width: 100%;
        max-width: 1920px;
    }
}



</style>