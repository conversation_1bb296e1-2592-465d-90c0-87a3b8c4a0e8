import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'

const store = createStore({
    state: {
        token: '',
        userInfo: null
    },

    mutations: {
        SET_TOKEN(state, token) {
            state.token = token
        },
        SET_USER_INFO(state, userInfo) {
            state.userInfo = userInfo
        },
        CLEAR_AUTH(state) {
            state.token = ''
            state.userInfo = null
        }
    },

    actions: {
        loginSuccess({ commit }, data) {
            const token = data.details.authorization
            commit('SET_TOKEN', token)
            commit('SET_USER_INFO', {
                name: data.details.name,
                roles: data.details.roles
            })
        },

        logout({ commit }) {
            commit('CLEAR_AUTH')
        }
    },

    getters: {
        isAuthenticated: state => !!state.token,
        getToken: state => state.token,
        getUserInfo: state => state.userInfo
    },

    plugins: [
        createPersistedState({
            key: 'vuex_store',
            paths: ['token', 'userInfo'],
            storage: window.localStorage
        })
    ]
})

export default store 